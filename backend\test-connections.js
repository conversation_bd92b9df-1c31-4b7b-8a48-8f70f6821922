/**
 * ScaleGrid Connection Test Script
 * 
 * Run this script to test ScaleGrid PostgreSQL and Redis connections
 * Usage: node test-connections.js
 */

require('dotenv').config();
const { Pool } = require('pg');
const Redis = require('ioredis');
const dns = require('dns');
const { promisify } = require('util');

const dnsLookup = promisify(dns.lookup);

async function testDNS(hostname) {
  console.log(`🔍 Testing DNS resolution for ${hostname}...`);
  
  try {
    const startTime = Date.now();
    const result = await Promise.race([
      dnsLookup(hostname, { all: true }),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('DNS timeout after 10s')), 10000)
      )
    ]);
    
    const latency = Date.now() - startTime;
    const addresses = result.map(addr => addr.address);
    
    console.log(`✅ DNS resolution successful (${latency}ms)`);
    console.log(`   Addresses: ${addresses.join(', ')}`);
    return { success: true, addresses, latency };
    
  } catch (error) {
    console.log(`❌ DNS resolution failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function testPostgreSQL() {
  console.log('\n🐘 Testing PostgreSQL Connection...');
  console.log(`Host: ${process.env.SCALEGRID_PG_HOST}`);
  console.log(`Port: ${process.env.SCALEGRID_PG_PORT}`);
  console.log(`Database: ${process.env.SCALEGRID_PG_DATABASE}`);
  console.log(`User: ${process.env.SCALEGRID_PG_USER}`);
  
  // Test DNS first
  const dnsResult = await testDNS(process.env.SCALEGRID_PG_HOST);
  if (!dnsResult.success) {
    return { success: false, error: 'DNS resolution failed' };
  }
  
  try {
    const startTime = Date.now();
    
    const pool = new Pool({
      host: process.env.SCALEGRID_PG_HOST,
      port: parseInt(process.env.SCALEGRID_PG_PORT || '6432'),
      database: process.env.SCALEGRID_PG_DATABASE || 'postgres',
      user: process.env.SCALEGRID_PG_USER,
      password: process.env.SCALEGRID_PG_PASSWORD,
      ssl: process.env.SCALEGRID_PG_SSL === 'true' ? {
        rejectUnauthorized: false
      } : false,
      connectionTimeoutMillis: 30000,
      query_timeout: 15000,
      max: 1
    });
    
    console.log('🔄 Attempting PostgreSQL connection...');
    
    const client = await Promise.race([
      pool.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout after 30s')), 30000)
      )
    ]);
    
    console.log('🔄 Testing query...');
    const result = await client.query('SELECT 1 as test, version() as version');
    
    client.release();
    await pool.end();
    
    const latency = Date.now() - startTime;
    console.log(`✅ PostgreSQL connection successful (${latency}ms)`);
    console.log(`   Version: ${result.rows[0].version}`);
    
    return { success: true, latency, version: result.rows[0].version };
    
  } catch (error) {
    console.log(`❌ PostgreSQL connection failed: ${error.message}`);
    console.log(`   Error type: ${error.constructor.name}`);
    if (error.code) console.log(`   Error code: ${error.code}`);
    return { success: false, error: error.message, code: error.code };
  }
}

async function testRedis() {
  console.log('\n🔴 Testing Redis Connection...');
  console.log(`Host: ${process.env.SCALEGRID_REDIS_HOST}`);
  console.log(`Port: ${process.env.SCALEGRID_REDIS_PORT}`);
  
  // Test DNS first
  const dnsResult = await testDNS(process.env.SCALEGRID_REDIS_HOST);
  if (!dnsResult.success) {
    return { success: false, error: 'DNS resolution failed' };
  }
  
  try {
    const startTime = Date.now();
    
    const redis = new Redis({
      host: process.env.SCALEGRID_REDIS_HOST,
      port: parseInt(process.env.SCALEGRID_REDIS_PORT || '6379'),
      password: process.env.SCALEGRID_REDIS_PASSWORD,
      connectTimeout: 30000,
      commandTimeout: 15000,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });
    
    console.log('🔄 Attempting Redis connection...');
    
    await Promise.race([
      redis.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout after 30s')), 30000)
      )
    ]);
    
    console.log('🔄 Testing ping...');
    const pong = await redis.ping();
    
    console.log('🔄 Testing info...');
    const info = await redis.info('server');
    
    await redis.disconnect();
    
    const latency = Date.now() - startTime;
    console.log(`✅ Redis connection successful (${latency}ms)`);
    console.log(`   Ping response: ${pong}`);
    
    return { success: true, latency, ping: pong };
    
  } catch (error) {
    console.log(`❌ Redis connection failed: ${error.message}`);
    console.log(`   Error type: ${error.constructor.name}`);
    if (error.code) console.log(`   Error code: ${error.code}`);
    return { success: false, error: error.message, code: error.code };
  }
}

async function main() {
  console.log('🚀 ScaleGrid Connection Test Suite');
  console.log('=====================================\n');
  
  const results = [];
  
  // Test PostgreSQL
  results.push(await testPostgreSQL());
  
  // Test Redis
  results.push(await testRedis());
  
  // Summary
  console.log('\n📊 Test Summary');
  console.log('================');
  
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`Total tests: ${total}`);
  console.log(`Successful: ${successful}`);
  console.log(`Failed: ${total - successful}`);
  
  if (successful === total) {
    console.log('\n🎉 All connections successful! ScaleGrid is ready.');
    process.exit(0);
  } else {
    console.log('\n⚠️ Some connections failed. Check network/firewall settings.');
    process.exit(1);
  }
}

main().catch(error => {
  console.error('💥 Test suite crashed:', error);
  process.exit(1);
});
