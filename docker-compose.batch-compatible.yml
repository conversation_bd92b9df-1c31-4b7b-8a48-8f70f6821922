version: '3.8'

services:
  # PostgreSQL Database - Compatible with batch file configuration
  postgres:
    image: postgres:15-alpine
    container_name: postgres-batch-compatible
    environment:
      POSTGRES_DB: x_marketing_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_batch_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
      - ./logs/postgres:/var/log/postgresql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d x_marketing_platform"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements,uuid-ossp,pg_trgm,btree_gin,btree_gist
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
      -c max_worker_processes=8
      -c max_parallel_workers_per_gather=4
      -c max_parallel_workers=8
      -c max_parallel_maintenance_workers=4
      -c log_statement=all
      -c log_duration=on
      -c log_min_duration_statement=1000

  # Redis Cache - Compatible with batch file configuration
  redis:
    image: redis:7-alpine
    container_name: redis-batch-compatible
    ports:
      - "6379:6379"
    volumes:
      - redis_batch_data:/data
      - ./docker/redis/conf/redis.conf:/usr/local/etc/redis/redis.conf
      - ./logs/redis:/var/log/redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s
    command: redis-server /usr/local/etc/redis/redis.conf
    sysctls:
      - net.core.somaxconn=65535

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin-batch-compatible
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_batch_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander-batch-compatible
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis-batch-compatible:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin
    ports:
      - "8081:8081"
    depends_on:
      redis:
        condition: service_healthy

volumes:
  postgres_batch_data:
    driver: local
  redis_batch_data:
    driver: local
  pgadmin_batch_data:
    driver: local

networks:
  default:
    name: batch-compatible-network
    driver: bridge
