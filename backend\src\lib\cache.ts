import { logger } from '../utils/logger';
import { enterpriseRedisManager } from '../config/redis';
import { redisCircuitBreaker } from '../middleware/circuitBreaker';
import { withRedisTimeout, withRedisFallback } from '../middleware/gracefulDegradation';

export class CacheManager {
  private redis: any = null;
  private defaultTTL = 3600; // 1 hour
  private isConnected = false;
  private memoryCache = new Map<string, { value: any; expires: number }>();

  constructor() {
    logger.info('Cache manager initialized with Redis fallback to in-memory');
  }

  async connect(): Promise<void> {
    try {
      // Use the Enterprise Redis Manager singleton
      await enterpriseRedisManager.initialize();

      // CRITICAL FIX: Ensure we get the main client, not subscriber client
      this.redis = enterpriseRedisManager.getClient();

      // Verify we have the correct client type (not subscriber)
      if (this.redis) {
        try {
          // Test with a regular operation (not subscriber command)
          await this.redis.ping();

          // Additional verification: try a SET operation to ensure it's not in subscriber mode
          await this.redis.set('cache_test_key', 'test_value', 'EX', 1);
          await this.redis.del('cache_test_key');

          this.isConnected = true;
          logger.info('✅ Cache connected to ScaleGrid Redis via Enterprise Redis Manager (verified non-subscriber)');
        } catch (redisError: any) {
          if (redisError.message?.includes('subscriber mode')) {
            logger.error('❌ Cache got subscriber client instead of main client, falling back to memory');
            this.redis = null;
            this.isConnected = true; // Use memory fallback
          } else {
            throw redisError;
          }
        }
      } else {
        logger.warn('⚠️ Enterprise Redis Manager not ready, Redis client is null');
        this.isConnected = true; // Still connected, just using memory
      }
    } catch (error: any) {
      logger.warn(`Cache Redis connection failed: ${error.message}, using in-memory cache fallback`);
      this.isConnected = true; // Still connected, just using memory
    }
  }

  async disconnect(): Promise<void> {
    this.memoryCache.clear();
    this.isConnected = false;
    logger.info('Cache disconnected (in-memory mode)');
  }

  // Enhanced cache methods with direct Redis usage
  async get<T>(key: string): Promise<T | null> {
    try {
      // Always try to get fresh Redis client in case it was reconnected
      const redisClient = enterpriseRedisManager.getClient();

      if (redisClient && enterpriseRedisManager.isConnected()) {
        try {
          return await redisCircuitBreaker.execute(async () => {
            return await withRedisTimeout(
              async () => {
                const value = await redisClient.get(key);
                return value ? JSON.parse(value) : null;
              },
              5000,
              `cache:get:${key}`
            );
          });
        } catch (redisError) {
          logger.debug(`Redis get failed for key ${key}, falling back to memory cache:`, redisError);
          return this.getFromMemoryCache(key);
        }
      } else {
        logger.debug(`Redis not available for key ${key}, using memory cache`);
        return this.getFromMemoryCache(key);
      }
    } catch (error) {
      logger.debug(`Cache get failed for key ${key}, using memory cache:`, error);
      return this.getFromMemoryCache(key);
    }
  }

  private getFromMemoryCache<T>(key: string): T | null {
    try {
      const cached = this.memoryCache.get(key);
      if (!cached) return null;

      if (Date.now() > cached.expires) {
        this.memoryCache.delete(key);
        return null;
      }

      return cached.value;
    } catch (error) {
      logger.error('Memory cache get error:', { key, error });
      return null;
    }
  }

  async set(key: string, value: any, ttl: number = this.defaultTTL): Promise<void> {
    try {
      // Always try to get fresh Redis client in case it was reconnected
      const redisClient = enterpriseRedisManager.getClient();

      if (redisClient && enterpriseRedisManager.isConnected()) {
        try {
          // Use enterprise Redis manager directly
          const success = await enterpriseRedisManager.set(key, value, ttl);
          if (success) {
            logger.debug(`Redis cache set: ${key} (TTL: ${ttl}s)`);
            return;
          }
        } catch (redisError) {
          logger.debug(`Redis set failed for key ${key}, falling back to memory cache:`, redisError);
        }
      }

      // Fallback to memory cache
      const expires = Date.now() + (ttl * 1000);
      this.memoryCache.set(key, { value, expires });
      logger.debug(`Memory cache set: ${key} (TTL: ${ttl}s)`);
    } catch (error) {
      logger.error('Cache set error:', { key, ttl, error });
    }
  }

  async del(key: string): Promise<void> {
    try {
      // Always try to get fresh Redis client in case it was reconnected
      const redisClient = enterpriseRedisManager.getClient();

      if (redisClient && enterpriseRedisManager.isConnected()) {
        try {
          // Use enterprise Redis manager directly
          await enterpriseRedisManager.del(key);
          logger.debug(`Redis cache deleted: ${key}`);
        } catch (redisError) {
          logger.debug(`Redis delete failed for key ${key}:`, redisError);
        }
      }

      // Also remove from memory cache
      this.memoryCache.delete(key);
      logger.debug(`Memory cache deleted: ${key}`);
    } catch (error) {
      logger.error('Cache delete error:', { key, error });
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      // In-memory implementation
      return this.memoryCache.has(key);
    } catch (error) {
      logger.error('Cache exists error:', { key, error });
      return false;
    }
  }

  // User session caching
  async cacheUserSession(userId: string, sessionData: any): Promise<void> {
    const key = `session:${userId}`;
    await this.set(key, sessionData, 86400); // 24 hours
  }

  async getUserSession(userId: string): Promise<any> {
    const key = `session:${userId}`;
    return await this.get(key);
  }

  async invalidateUserSession(userId: string): Promise<void> {
    const key = `session:${userId}`;
    await this.del(key);
  }

  // Analytics caching
  async cacheAnalytics(accountId: string, period: string, data: any): Promise<void> {
    const key = `analytics:${accountId}:${period}`;
    await this.set(key, data, 1800); // 30 minutes
  }

  async getAnalytics(accountId: string, period: string): Promise<any> {
    const key = `analytics:${accountId}:${period}`;
    return await this.get(key);
  }

  // Content template caching
  async cacheContentTemplates(category: string, templates: any[]): Promise<void> {
    const key = `templates:${category}`;
    await this.set(key, templates, 7200); // 2 hours
  }

  async getContentTemplates(category: string): Promise<any[]> {
    const key = `templates:${category}`;
    return await this.get(key) || [];
  }

  // Trending hashtags caching
  async cacheTrendingHashtags(hashtags: any[]): Promise<void> {
    const key = 'trending:hashtags';
    await this.set(key, hashtags, 900); // 15 minutes
  }

  async getTrendingHashtags(): Promise<any[]> {
    const key = 'trending:hashtags';
    return await this.get(key) || [];
  }

  // Rate limiting cache
  async incrementRateLimit(key: string, windowMs: number): Promise<number> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      // Check if Redis is healthy before using it
      if (this.redis && await enterpriseRedisManager.isHealthy()) {
        const current = await this.redis.incr(key);
        if (current === 1) {
          await this.redis.expire(key, Math.ceil(windowMs / 1000));
        }
        return current;
      } else {
        // Fallback to in-memory rate limiting (simplified)
        logger.warn('Redis not available for rate limiting, using in-memory fallback');
        return 1; // Allow request when Redis is not available
      }
    } catch (error) {
      logger.error('Rate limit increment error:', { key, error });
      return 0;
    }
  }

  // Cache warming strategies
  async warmCache(): Promise<void> {
    logger.info('Starting cache warming...');
    
    try {
      // Warm content templates
      const categories = ['crypto', 'finance', 'general', 'news'];
      for (const category of categories) {
        // This would typically fetch from database
        // await this.cacheContentTemplates(category, templates);
      }

      // Warm trending hashtags
      // await this.cacheTrendingHashtags(trendingData);

      logger.info('Cache warming completed');
    } catch (error) {
      logger.error('Cache warming failed:', error);
    }
  }

  // Cache invalidation patterns
  async invalidatePattern(pattern: string): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      // In-memory implementation - simple pattern matching
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      const keysToDelete = Array.from(this.memoryCache.keys()).filter(key => regex.test(key));
      keysToDelete.forEach(key => this.memoryCache.delete(key));
    } catch (error) {
      logger.error('Pattern invalidation error:', { pattern, error });
    }
  }

  // Bulk operations
  async mget(keys: string[]): Promise<(any | null)[]> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      // In-memory implementation
      return keys.map(key => {
        const cached = this.memoryCache.get(key);
        if (!cached) return null;

        if (Date.now() > cached.expires) {
          this.memoryCache.delete(key);
          return null;
        }

        return cached.value;
      });
    } catch (error) {
      logger.error('Bulk get error:', { keys, error });
      return keys.map(() => null);
    }
  }

  async mset(keyValuePairs: Array<{key: string, value: any, ttl?: number}>): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      // In-memory implementation
      keyValuePairs.forEach(({key, value, ttl = this.defaultTTL}) => {
        const expires = Date.now() + (ttl * 1000);
        this.memoryCache.set(key, { value, expires });
      });
    } catch (error) {
      logger.error('Bulk set error:', error);
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      // Check Redis connection if available
      if (this.redis) {
        try {
          await this.redis.ping();
          return true;
        } catch (redisError) {
          logger.warn('Redis ping failed, falling back to in-memory cache:', redisError);
          // Redis failed, but in-memory cache is still available
          return true;
        }
      }

      // In-memory implementation - always healthy
      return true;
    } catch (error) {
      logger.error('Cache health check failed:', error);
      return false;
    }
  }

  /**
   * Get the Redis client instance for advanced operations
   */
  getRedisClient(): any {
    return this.redis;
  }
}

// Singleton instance
export const cacheManager = new CacheManager();

// Cache middleware for Express
export const cacheMiddleware = (ttl: number = 300) => {
  return async (req: any, res: any, next: any) => {
    const key = `cache:${req.method}:${req.originalUrl}`;
    
    try {
      const cached = await cacheManager.get(key);
      if (cached) {
        return res.json(cached);
      }

      // Override res.json to cache the response
      const originalJson = res.json;
      res.json = function(data: any) {
        cacheManager.set(key, data, ttl);
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      logger.error('Cache middleware error:', error);
      next();
    }
  };
};


