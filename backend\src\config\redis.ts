import Redis, { Cluster, ClusterOptions, RedisOptions } from 'ioredis';
import { logger } from '../utils/logger';
import { trace, SpanStatusCode, SpanKind } from '@opentelemetry/api';
import { EventEmitter } from 'events';
import dns from 'dns';
import { promisify } from 'util';
import { createRedisAdapter, UnifiedRedisAdapter } from '../adapters/redisAdapter';

// Enterprise Redis interfaces
interface RedisMetrics {
  operations: number;
  hits: number;
  misses: number;
  errors: number;
  averageResponseTime: number;
  memoryUsage: number;
  connectedClients: number;
  keyspaceHits: number;
  keyspaceMisses: number;
  evictedKeys: number;
  // Enhanced metrics for connection resilience
  connectionAttempts: number;
  connectionFailures: number;
  dnsResolutionTime: number;
  circuitBreakerTrips: number;
  reconnectionCount: number;
}

// DNS Resolution Cache Interface
interface DNSCacheEntry {
  addresses: string[];
  timestamp: number;
  ttl: number;
}

// Connection Health Status
interface ConnectionHealth {
  isHealthy: boolean;
  lastHealthCheck: number;
  consecutiveFailures: number;
  averageLatency: number;
  endpoint: string;
}

// Circuit Breaker State
interface CircuitBreakerState {
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  failureCount: number;
  lastFailureTime: number;
  nextAttemptTime: number;
  successCount: number;
}

// Enhanced Connection Pool Configuration
interface EnhancedConnectionConfig {
  // DNS Resolution
  dnsCache: {
    enabled: boolean;
    ttl: number;
    maxEntries: number;
  };
  // Connection Pre-warming
  preWarm: {
    enabled: boolean;
    connectionCount: number;
    healthCheckInterval: number;
  };
  // Circuit Breaker
  circuitBreaker: {
    enabled: boolean;
    failureThreshold: number;
    resetTimeout: number;
    halfOpenMaxCalls: number;
  };
  // Load Balancing
  loadBalancing: {
    enabled: boolean;
    strategy: 'round_robin' | 'least_connections' | 'weighted';
    healthCheckInterval: number;
  };
}

interface RedisClusterNode {
  host: string;
  port: number;
  role?: 'master' | 'slave';
  status?: 'connected' | 'disconnected' | 'connecting';
}

interface RedisConfiguration {
  url: string;
  cluster: {
    enabled: boolean;
    nodes: RedisClusterNode[];
    options: any;
  };
  sentinel: {
    enabled: boolean;
    sentinels: Array<{ host: string; port: number }>;
    name: string;
  };
  performance: {
    enablePipelining: boolean;
    enableCompression: boolean;
    maxMemoryPolicy: string;
    keyPrefix: string;
  };
  monitoring: {
    enableMetrics: boolean;
    metricsInterval: number;
    slowLogThreshold: number;
  };
}

/**
 * Enterprise Redis Manager - 2025 Edition
 * Advanced Redis management with clustering, monitoring, and performance optimization
 */
export class EnterpriseRedisManager extends EventEmitter {
  private static instance: EnterpriseRedisManager;
  private redisClient: Redis.Redis | null = null;
  private redisCluster: Cluster | null = null;
  private subscriberClient: Redis.Redis | null = null;
  private publisherClient: Redis.Redis | null = null;
  private configuration: RedisConfiguration = {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    cluster: {
      enabled: process.env.REDIS_CLUSTER_ENABLED === 'true',
      nodes: [],
      options: {}
    },
    sentinel: {
      enabled: process.env.REDIS_SENTINEL_ENABLED === 'true',
      sentinels: [],
      name: process.env.REDIS_SENTINEL_NAME || 'mymaster'
    },
    performance: {
      enablePipelining: process.env.REDIS_ENABLE_PIPELINING !== 'false',
      enableCompression: process.env.REDIS_ENABLE_COMPRESSION === 'true',
      maxMemoryPolicy: process.env.REDIS_MAX_MEMORY_POLICY || 'allkeys-lru',
      keyPrefix: process.env.REDIS_KEY_PREFIX || 'x-marketing:'
    },
    monitoring: {
      enableMetrics: process.env.REDIS_ENABLE_METRICS !== 'false',
      metricsInterval: parseInt(process.env.REDIS_METRICS_INTERVAL || '60000'),
      slowLogThreshold: parseInt(process.env.REDIS_SLOW_LOG_THRESHOLD || '10000')
    }
  };
  private metrics: RedisMetrics = {
    operations: 0,
    hits: 0,
    misses: 0,
    errors: 0,
    averageResponseTime: 0,
    memoryUsage: 0,
    connectedClients: 0,
    keyspaceHits: 0,
    keyspaceMisses: 0,
    evictedKeys: 0,
    // Enhanced metrics for connection resilience
    connectionAttempts: 0,
    connectionFailures: 0,
    dnsResolutionTime: 0,
    circuitBreakerTrips: 0,
    reconnectionCount: 0
  };
  private metricsInterval: NodeJS.Timeout | null = null;
  private tracer = trace.getTracer('enterprise-redis-manager', '1.0.0');
  private isInitialized = false;

  // Enterprise Connection Resilience Components
  private dnsCache: Map<string, DNSCacheEntry> = new Map();
  private connectionHealth: Map<string, ConnectionHealth> = new Map();
  private circuitBreakerState: Map<string, CircuitBreakerState> = new Map();
  private enhancedConfig!: EnhancedConnectionConfig;
  private dnsLookup = promisify(dns.lookup);
  private preWarmedConnections: Map<string, Redis.Redis[]> = new Map();
  private connectionLoadBalancer: Map<string, number> = new Map();

  constructor() {
    super();
    this.initializeEnhancedConfiguration();
    this.initializeConfiguration();
    this.initializeMetrics();
    this.initializeDNSCache();
    this.initializeCircuitBreakers();
  }

  static getInstance(): EnterpriseRedisManager {
    if (!EnterpriseRedisManager.instance) {
      EnterpriseRedisManager.instance = new EnterpriseRedisManager();
    }
    return EnterpriseRedisManager.instance;
  }

  /**
   * Initialize enhanced connection configuration
   */
  private initializeEnhancedConfiguration(): void {
    this.enhancedConfig = {
      dnsCache: {
        enabled: process.env.REDIS_DNS_CACHE_ENABLED !== 'false',
        ttl: parseInt(process.env.REDIS_DNS_CACHE_TTL || '300000'), // 5 minutes
        maxEntries: parseInt(process.env.REDIS_DNS_CACHE_MAX_ENTRIES || '100')
      },
      preWarm: {
        enabled: process.env.REDIS_PREWARM_ENABLED !== 'false',
        connectionCount: parseInt(process.env.REDIS_PREWARM_CONNECTIONS || '2'),
        healthCheckInterval: parseInt(process.env.REDIS_PREWARM_HEALTH_INTERVAL || '30000')
      },
      circuitBreaker: {
        enabled: process.env.REDIS_CIRCUIT_BREAKER_ENABLED !== 'false',
        failureThreshold: parseInt(process.env.REDIS_CIRCUIT_BREAKER_THRESHOLD || '5'),
        resetTimeout: parseInt(process.env.REDIS_CIRCUIT_BREAKER_RESET_TIMEOUT || '60000'),
        halfOpenMaxCalls: parseInt(process.env.REDIS_CIRCUIT_BREAKER_HALF_OPEN_CALLS || '3')
      },
      loadBalancing: {
        enabled: process.env.REDIS_LOAD_BALANCING_ENABLED !== 'false',
        strategy: (process.env.REDIS_LOAD_BALANCING_STRATEGY as any) || 'round_robin',
        healthCheckInterval: parseInt(process.env.REDIS_LOAD_BALANCING_HEALTH_INTERVAL || '15000')
      }
    };

    logger.info('✅ Enhanced Redis connection configuration initialized', {
      dnsCache: this.enhancedConfig.dnsCache.enabled,
      preWarm: this.enhancedConfig.preWarm.enabled,
      circuitBreaker: this.enhancedConfig.circuitBreaker.enabled,
      loadBalancing: this.enhancedConfig.loadBalancing.enabled
    });
  }

  /**
   * Initialize DNS cache for improved resolution performance
   */
  private initializeDNSCache(): void {
    if (!this.enhancedConfig.dnsCache.enabled) return;

    // Clean up expired DNS entries periodically
    setInterval(() => {
      const now = Date.now();
      for (const [hostname, entry] of this.dnsCache.entries()) {
        if (now - entry.timestamp > entry.ttl) {
          this.dnsCache.delete(hostname);
          logger.debug(`DNS cache entry expired for ${hostname}`);
        }
      }
    }, 60000); // Clean every minute

    logger.info('✅ DNS cache initialized for Redis connections');
  }

  /**
   * Initialize circuit breakers for connection resilience
   */
  private initializeCircuitBreakers(): void {
    if (!this.enhancedConfig.circuitBreaker.enabled) return;

    // Initialize circuit breaker states for known endpoints
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const urlParts = new URL(redisUrl);
    const endpoint = `${urlParts.hostname}:${urlParts.port}`;

    this.circuitBreakerState.set(endpoint, {
      state: 'CLOSED',
      failureCount: 0,
      lastFailureTime: 0,
      nextAttemptTime: 0,
      successCount: 0
    });

    logger.info('✅ Circuit breakers initialized for Redis connections');
  }

  /**
   * Enhanced DNS resolution with caching and retry logic
   */
  private async resolveDNSWithCache(hostname: string): Promise<string[]> {
    const startTime = Date.now();

    try {
      // Check DNS cache first
      if (this.enhancedConfig.dnsCache.enabled) {
        const cached = this.dnsCache.get(hostname);
        if (cached && (Date.now() - cached.timestamp) < cached.ttl) {
          logger.debug(`DNS cache hit for ${hostname}: ${cached.addresses.join(', ')}`);
          return cached.addresses;
        }
      }

      // Perform DNS resolution with retry logic
      const addresses = await this.performDNSResolutionWithRetry(hostname);

      // Cache the result
      if (this.enhancedConfig.dnsCache.enabled && addresses.length > 0) {
        this.dnsCache.set(hostname, {
          addresses,
          timestamp: Date.now(),
          ttl: this.enhancedConfig.dnsCache.ttl
        });

        // Limit cache size
        if (this.dnsCache.size > this.enhancedConfig.dnsCache.maxEntries) {
          const oldestKey = this.dnsCache.keys().next().value;
          if (oldestKey) {
            this.dnsCache.delete(oldestKey);
          }
        }
      }

      this.metrics.dnsResolutionTime = Date.now() - startTime;
      return addresses;

    } catch (error) {
      this.metrics.dnsResolutionTime = Date.now() - startTime;
      logger.error(`DNS resolution failed for ${hostname}:`, error);
      throw error;
    }
  }

  /**
   * Perform DNS resolution with exponential backoff retry
   */
  private async performDNSResolutionWithRetry(hostname: string, maxRetries: number = 3): Promise<string[]> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.debug(`DNS resolution attempt ${attempt}/${maxRetries} for ${hostname}`);

        const result = await Promise.race([
          this.dnsLookup(hostname, { all: true }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('DNS resolution timeout')), 10000)
          )
        ]) as dns.LookupAddress[];

        const addresses = result.map(addr => addr.address);
        logger.debug(`DNS resolution successful for ${hostname}: ${addresses.join(', ')}`);
        return addresses;

      } catch (error) {
        lastError = error as Error;
        logger.warn(`DNS resolution attempt ${attempt} failed for ${hostname}:`, error);

        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Exponential backoff, max 10s
          logger.debug(`Retrying DNS resolution for ${hostname} in ${delay}ms`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error(`DNS resolution failed for ${hostname} after ${maxRetries} attempts`);
  }

  /**
   * Check if circuit breaker allows connection attempt
   */
  private canAttemptConnection(endpoint: string): boolean {
    if (!this.enhancedConfig.circuitBreaker.enabled) return true;

    const state = this.circuitBreakerState.get(endpoint);
    if (!state) return true;

    const now = Date.now();

    switch (state.state) {
      case 'CLOSED':
        return true;

      case 'OPEN':
        if (now >= state.nextAttemptTime) {
          // Transition to HALF_OPEN
          state.state = 'HALF_OPEN';
          state.successCount = 0;
          logger.info(`Circuit breaker transitioning to HALF_OPEN for ${endpoint}`);
          return true;
        }
        return false;

      case 'HALF_OPEN':
        return state.successCount < this.enhancedConfig.circuitBreaker.halfOpenMaxCalls;

      default:
        return true;
    }
  }

  /**
   * Record connection success for circuit breaker
   */
  private recordConnectionSuccess(endpoint: string): void {
    if (!this.enhancedConfig.circuitBreaker.enabled) return;

    const state = this.circuitBreakerState.get(endpoint);
    if (!state) return;

    state.successCount++;

    if (state.state === 'HALF_OPEN' &&
        state.successCount >= this.enhancedConfig.circuitBreaker.halfOpenMaxCalls) {
      // Transition back to CLOSED
      state.state = 'CLOSED';
      state.failureCount = 0;
      logger.info(`Circuit breaker closed for ${endpoint} after successful recovery`);
    }
  }

  /**
   * Record connection failure for circuit breaker
   */
  private recordConnectionFailure(endpoint: string): void {
    if (!this.enhancedConfig.circuitBreaker.enabled) return;

    const state = this.circuitBreakerState.get(endpoint);
    if (!state) return;

    state.failureCount++;
    state.lastFailureTime = Date.now();
    this.metrics.connectionFailures++;

    if (state.failureCount >= this.enhancedConfig.circuitBreaker.failureThreshold) {
      // Open the circuit breaker
      state.state = 'OPEN';
      state.nextAttemptTime = Date.now() + this.enhancedConfig.circuitBreaker.resetTimeout;
      this.metrics.circuitBreakerTrips++;
      logger.warn(`Circuit breaker opened for ${endpoint} after ${state.failureCount} failures`);
    }
  }

  /**
   * Initialize enterprise Redis configuration
   */
  private initializeConfiguration(): void {
    // Parse ScaleGrid Redis URL for proper configuration
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const isScaleGrid = redisUrl.includes('mongodirector.com');

    this.configuration = {
      url: redisUrl,
      cluster: {
        enabled: process.env.REDIS_CLUSTER_ENABLED === 'true',
        nodes: this.parseClusterNodes(process.env.REDIS_CLUSTER_NODES || ''),
        options: {
          enableReadyCheck: true,
          redisOptions: {
            password: process.env.SCALEGRID_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
            db: parseInt(process.env.REDIS_DB || '0'),
            retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY_ON_FAILOVER || '100'),
            maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES_PER_REQUEST || '3'),
            lazyConnect: true,
            keepAlive: 30000,
            connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT || '30000'),
            commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT || '10000'),
            enableAutoPipelining: true,
            family: 4,
            // ScaleGrid Redis - TLS not required for this instance
            tls: process.env.REDIS_TLS_ENABLED === 'true' ? {
              rejectUnauthorized: false,
              servername: process.env.SCALEGRID_REDIS_HOST
            } : undefined,
            // Prevent subscriber mode issues
            enableReadyCheck: true
          }
        }
      },
      sentinel: {
        enabled: process.env.REDIS_SENTINEL_ENABLED === 'true',
        sentinels: this.parseSentinels(process.env.REDIS_SENTINELS || ''),
        name: process.env.REDIS_SENTINEL_NAME || 'mymaster'
      },
      performance: {
        enablePipelining: process.env.REDIS_ENABLE_PIPELINING !== 'false',
        enableCompression: process.env.REDIS_ENABLE_COMPRESSION === 'true',
        maxMemoryPolicy: process.env.REDIS_MAX_MEMORY_POLICY || 'allkeys-lru',
        keyPrefix: process.env.REDIS_KEY_PREFIX || 'x-marketing:'
      },
      monitoring: {
        enableMetrics: process.env.REDIS_ENABLE_METRICS !== 'false',
        metricsInterval: parseInt(process.env.REDIS_METRICS_INTERVAL || '60000'),
        slowLogThreshold: parseInt(process.env.REDIS_SLOW_LOG_THRESHOLD || '10000')
      }
    };
  }

  /**
   * Initialize metrics tracking
   */
  private initializeMetrics(): void {
    this.metrics = {
      operations: 0,
      hits: 0,
      misses: 0,
      errors: 0,
      averageResponseTime: 0,
      memoryUsage: 0,
      connectedClients: 0,
      keyspaceHits: 0,
      keyspaceMisses: 0,
      evictedKeys: 0,
      // Enhanced metrics for connection resilience
      connectionAttempts: 0,
      connectionFailures: 0,
      dnsResolutionTime: 0,
      circuitBreakerTrips: 0,
      reconnectionCount: 0
    };
  }

  /**
   * Parse cluster nodes from environment variable
   */
  private parseClusterNodes(nodesString: string): RedisClusterNode[] {
    if (!nodesString) {
      return [{ host: 'localhost', port: 6379 }];
    }

    return nodesString.split(',').map(node => {
      const [host, port] = node.trim().split(':');
      return {
        host: host || 'localhost',
        port: parseInt(port || '6379') || 6379
      };
    });
  }

  /**
   * Parse sentinel nodes from environment variable
   */
  private parseSentinels(sentinelsString: string): Array<{ host: string; port: number }> {
    if (!sentinelsString) {
      return [];
    }

    return sentinelsString.split(',').map(sentinel => {
      const [host, port] = sentinel.trim().split(':');
      return {
        host: host || 'localhost',
        port: parseInt(port || '26379') || 26379
      };
    });
  }

  /**
   * Initialize enterprise Redis connections
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('Enterprise Redis Manager already initialized');
      return;
    }

    const span = this.tracer.startSpan('redis_manager_initialize', {
      kind: SpanKind.INTERNAL
    });

    try {
      // Check if Redis is disabled
      if (process.env.DISABLE_REDIS === 'true') {
        logger.warn('⚠️ Redis is disabled via DISABLE_REDIS environment variable');
        this.isInitialized = true;
        span.setStatus({ code: SpanStatusCode.OK });
        logger.info('✅ Enterprise Redis Manager initialized in disabled mode');
        return;
      }

      logger.info('🚀 Initializing Enterprise Redis Manager...');

      if (this.configuration.cluster.enabled) {
        await this.initializeCluster();
      } else if (this.configuration.sentinel.enabled) {
        await this.initializeSentinel();
      } else {
        await this.initializeStandalone();
      }

      // Start metrics collection
      if (this.configuration.monitoring.enableMetrics) {
        this.startMetricsCollection();
      }

      // Start subscriber mode monitoring
      this.startSubscriberModeMonitoring();

      this.isInitialized = true;
      span.setStatus({ code: SpanStatusCode.OK });
      logger.info('✅ Enterprise Redis Manager initialized successfully');

    } catch (error) {
      span.recordException(error as Error);
      span.setStatus({ code: SpanStatusCode.ERROR, message: (error as Error).message });
      logger.error('❌ Failed to initialize Enterprise Redis Manager:', error);
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Initialize Redis cluster
   */
  private async initializeCluster(): Promise<void> {
    logger.info('🔄 Initializing Redis Cluster...');

    this.redisCluster = new Cluster(
      this.configuration.cluster.nodes,
      this.configuration.cluster.options
    );

    this.setupClusterEventListeners();

    // Test cluster connection
    await this.redisCluster.ping();
    logger.info('✅ Redis Cluster connected successfully');
  }

  /**
   * Initialize Redis with Sentinel
   */
  private async initializeSentinel(): Promise<void> {
    logger.info('🔄 Initializing Redis with Sentinel...');

    this.redisClient = new Redis({
      sentinels: this.configuration.sentinel.sentinels,
      name: this.configuration.sentinel.name,
      ...this.configuration.cluster.options.redisOptions
    });

    this.setupStandaloneEventListeners();

    // Test sentinel connection
    await this.redisClient?.ping();
    logger.info('✅ Redis Sentinel connected successfully');
  }

  /**
   * Initialize standalone Redis with enterprise-grade resilience
   */
  private async initializeStandalone(): Promise<void> {
    logger.info('🔄 Initializing Standalone Redis with enterprise resilience...');

    const urlParts = new URL(this.configuration.url);
    const endpoint = `${urlParts.hostname}:${urlParts.port}`;

    // Check circuit breaker before attempting connection
    if (!this.canAttemptConnection(endpoint)) {
      throw new Error(`Circuit breaker is OPEN for Redis endpoint ${endpoint}`);
    }

    try {
      // Enhanced DNS resolution with caching
      const resolvedAddresses = await this.resolveDNSWithCache(urlParts.hostname);
      logger.info(`🔗 Resolved Redis addresses: ${resolvedAddresses.join(', ')}`);

      // Enhanced Redis options with resilience features
      const redisOptions: RedisOptions = {
        ...this.configuration.cluster.options.redisOptions,
        keyPrefix: this.configuration.performance.keyPrefix,
        // Connection resilience
        enableReadyCheck: true,
        lazyConnect: false,
        // Enhanced retry logic with exponential backoff
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 5, // Increased from 3
        // Improved timeouts
        connectTimeout: 45000, // Increased from 30s
        commandTimeout: 15000, // Increased from 10s
        // Connection keep-alive
        keepAlive: 30000,
        // Family preference for IPv4 (more reliable for ScaleGrid)
        family: 4,
        // Enhanced error handling
        enableOfflineQueue: false,
        // Connection pooling
        maxLoadingTimeout: 5000
      };

      this.metrics.connectionAttempts++;
      logger.info(`🔗 Connecting to Redis: ${urlParts.hostname}:${urlParts.port} (attempt ${this.metrics.connectionAttempts})`);

      this.redisClient = new Redis(this.configuration.url, redisOptions);

      // Create separate subscriber and publisher clients to avoid subscriber mode conflicts
      this.subscriberClient = new Redis(this.configuration.url, {
        ...redisOptions,
        keyPrefix: undefined // Remove keyPrefix for pub/sub operations
      });

      this.publisherClient = new Redis(this.configuration.url, {
        ...redisOptions,
        keyPrefix: undefined // Remove keyPrefix for pub/sub operations
      });

      this.setupStandaloneEventListeners();

      // Enhanced connection testing with individual timeouts and retries
      await this.testConnectionsWithResilience();

      // Record successful connection for circuit breaker
      this.recordConnectionSuccess(endpoint);

      // Initialize connection health monitoring
      this.initializeConnectionHealthMonitoring(endpoint);

      // Pre-warm connections if enabled
      if (this.enhancedConfig.preWarm.enabled) {
        await this.preWarmConnections(endpoint);
      }

      logger.info('✅ Standalone Redis connected successfully with enterprise resilience features');

    } catch (error) {
      // Record connection failure for circuit breaker
      this.recordConnectionFailure(endpoint);
      logger.error(`❌ Failed to initialize Redis with resilience: ${error}`);
      throw error;
    }
  }

  /**
   * Test connections with enhanced resilience
   */
  private async testConnectionsWithResilience(): Promise<void> {
    const connections = [
      { name: 'main', client: this.redisClient },
      { name: 'subscriber', client: this.subscriberClient },
      { name: 'publisher', client: this.publisherClient }
    ];

    for (const connection of connections) {
      if (!connection.client) continue;

      let lastError: Error | null = null;
      let success = false;

      // Retry each connection individually with exponential backoff
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          const startTime = Date.now();

          await Promise.race([
            connection.client.ping(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error(`${connection.name} ping timeout`)), 20000)
            )
          ]);

          const latency = Date.now() - startTime;
          logger.info(`✅ ${connection.name} Redis connection test successful (${latency}ms)`);
          success = true;
          break;

        } catch (error) {
          lastError = error as Error;
          logger.warn(`⚠️ ${connection.name} Redis connection test attempt ${attempt} failed:`, error);

          if (attempt < 3) {
            const delay = Math.min(2000 * Math.pow(2, attempt - 1), 10000);
            logger.info(`⏳ Retrying ${connection.name} connection test in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      if (!success) {
        throw lastError || new Error(`${connection.name} Redis connection test failed after all retries`);
      }
    }
  }

  /**
   * Initialize connection health monitoring
   */
  private initializeConnectionHealthMonitoring(endpoint: string): void {
    if (!this.enhancedConfig.loadBalancing.enabled) return;

    const healthCheck = {
      isHealthy: true,
      lastHealthCheck: Date.now(),
      consecutiveFailures: 0,
      averageLatency: 0,
      endpoint
    };

    this.connectionHealth.set(endpoint, healthCheck);

    // Start periodic health checks
    setInterval(async () => {
      await this.performHealthCheck(endpoint);
    }, this.enhancedConfig.loadBalancing.healthCheckInterval);

    logger.info(`✅ Health monitoring initialized for Redis endpoint ${endpoint}`);
  }

  /**
   * Perform health check for a Redis endpoint
   */
  private async performHealthCheck(endpoint: string): Promise<void> {
    const health = this.connectionHealth.get(endpoint);
    if (!health) return;

    try {
      const startTime = Date.now();

      if (this.redisClient) {
        await Promise.race([
          this.redisClient.ping(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Health check timeout')), 5000)
          )
        ]);
      }

      const latency = Date.now() - startTime;

      // Update health status
      health.isHealthy = true;
      health.lastHealthCheck = Date.now();
      health.consecutiveFailures = 0;
      health.averageLatency = (health.averageLatency + latency) / 2;

      logger.debug(`Health check passed for ${endpoint} (${latency}ms)`);

    } catch (error) {
      health.isHealthy = false;
      health.lastHealthCheck = Date.now();
      health.consecutiveFailures++;

      logger.warn(`Health check failed for ${endpoint}:`, error);

      // If too many consecutive failures, trigger circuit breaker
      if (health.consecutiveFailures >= 3) {
        this.recordConnectionFailure(endpoint);
      }
    }
  }

  /**
   * Pre-warm connections for improved performance
   */
  private async preWarmConnections(endpoint: string): Promise<void> {
    if (!this.enhancedConfig.preWarm.enabled) return;

    try {
      const connections: Redis.Redis[] = [];
      const connectionCount = this.enhancedConfig.preWarm.connectionCount;

      logger.info(`🔥 Pre-warming ${connectionCount} Redis connections for ${endpoint}...`);

      for (let i = 0; i < connectionCount; i++) {
        const connection = new Redis(this.configuration.url, {
          ...this.configuration.cluster.options.redisOptions,
          lazyConnect: false,
          enableReadyCheck: true
        });

        // Test the connection
        await connection.ping();
        connections.push(connection);

        logger.debug(`Pre-warmed connection ${i + 1}/${connectionCount} for ${endpoint}`);
      }

      this.preWarmedConnections.set(endpoint, connections);

      // Start periodic health checks for pre-warmed connections
      setInterval(async () => {
        await this.maintainPreWarmedConnections(endpoint);
      }, this.enhancedConfig.preWarm.healthCheckInterval);

      logger.info(`✅ ${connectionCount} Redis connections pre-warmed for ${endpoint}`);

    } catch (error) {
      logger.warn(`Failed to pre-warm connections for ${endpoint}:`, error);
      // Don't throw - pre-warming is optional
    }
  }

  /**
   * Maintain pre-warmed connections
   */
  private async maintainPreWarmedConnections(endpoint: string): Promise<void> {
    const connections = this.preWarmedConnections.get(endpoint);
    if (!connections) return;

    const healthyConnections: Redis.Redis[] = [];

    for (const connection of connections) {
      try {
        await Promise.race([
          connection.ping(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Pre-warm health check timeout')), 3000)
          )
        ]);
        healthyConnections.push(connection);
      } catch (error) {
        logger.debug(`Pre-warmed connection unhealthy for ${endpoint}, recreating...`);
        try {
          connection.disconnect();
        } catch (disconnectError) {
          // Ignore disconnect errors
        }

        // Create replacement connection
        try {
          const newConnection = new Redis(this.configuration.url, {
            ...this.configuration.cluster.options.redisOptions,
            lazyConnect: false,
            enableReadyCheck: true
          });
          await newConnection.ping();
          healthyConnections.push(newConnection);
        } catch (recreateError) {
          logger.warn(`Failed to recreate pre-warmed connection for ${endpoint}:`, recreateError);
        }
      }
    }

    this.preWarmedConnections.set(endpoint, healthyConnections);
  }

  /**
   * Setup event listeners for cluster
   */
  private setupClusterEventListeners(): void {
    if (!this.redisCluster) return;

    this.redisCluster.on('connect', () => {
      logger.info('✅ Redis Cluster connected');
      this.emit('cluster:connected');
    });

    this.redisCluster.on('ready', () => {
      logger.info('🚀 Redis Cluster ready');
      this.emit('cluster:ready');
    });

    this.redisCluster.on('error', (error) => {
      logger.error('❌ Redis Cluster error:', error);
      this.metrics.errors++;
      this.emit('cluster:error', error);
    });

    this.redisCluster.on('close', () => {
      logger.warn('🔌 Redis Cluster connection closed');
      this.emit('cluster:closed');
    });

    this.redisCluster.on('reconnecting', () => {
      logger.info('🔄 Redis Cluster reconnecting...');
      this.emit('cluster:reconnecting');
    });

    this.redisCluster.on('node error', (error, node) => {
      logger.error(`❌ Redis Cluster node error (${node.host}:${node.port}):`, error);
      this.emit('cluster:node_error', error, node);
    });
  }

  /**
   * Setup event listeners for standalone/sentinel
   */
  private setupStandaloneEventListeners(): void {
    if (!this.redisClient) return;

    this.redisClient.on('connect', () => {
      logger.info('✅ Redis connected');
      this.emit('redis:connected');
    });

    this.redisClient.on('ready', () => {
      logger.info('🚀 Redis ready');
      this.emit('redis:ready');
    });

    this.redisClient.on('error', (error: any) => {
      logger.error('❌ Redis error:', error);
      this.metrics.errors++;
      this.metrics.connectionFailures++;

      // Record failure for circuit breaker
      const urlParts = new URL(this.configuration.url);
      const endpoint = `${urlParts.hostname}:${urlParts.port}`;
      this.recordConnectionFailure(endpoint);

      this.emit('redis:error', error);
    });

    this.redisClient.on('close', () => {
      logger.warn('🔌 Redis connection closed');
      this.emit('redis:closed');
    });

    this.redisClient.on('reconnecting', () => {
      logger.info('🔄 Redis reconnecting...');
      this.emit('redis:reconnecting');
    });
  }

  /**
   * Start metrics collection
   */
  private startMetricsCollection(): void {
    this.metricsInterval = setInterval(async () => {
      await this.collectMetrics();
    }, this.configuration.monitoring.metricsInterval);

    logger.info(`📊 Redis metrics collection started (interval: ${this.configuration.monitoring.metricsInterval}ms)`);
  }

  /**
   * Start subscriber mode monitoring to detect and fix corruption
   */
  private startSubscriberModeMonitoring(): void {
    setInterval(() => {
      try {
        if (this.redisClient && this.isClientInSubscriberMode(this.redisClient)) {
          logger.error('🚨 DETECTED: Main Redis client corrupted with subscriber mode!');
          this.recreateMainClientImmediate();
        }
      } catch (error) {
        // Silent monitoring - don't spam logs
      }
    }, 5000); // Check every 5 seconds

    logger.info('✅ Redis subscriber mode monitoring started');
  }

  /**
   * Collect Redis metrics
   */
  private async collectMetrics(): Promise<void> {
    try {
      const client = this.getActiveClient();
      if (!client) return;

      const info = await client.info('stats');
      const memory = await client.info('memory');
      const clients = await client.info('clients');

      // Parse info strings
      const stats = this.parseRedisInfo(info);
      const memoryInfo = this.parseRedisInfo(memory);
      const clientsInfo = this.parseRedisInfo(clients);

      // Update metrics
      this.metrics.keyspaceHits = parseInt(stats.keyspace_hits || '0');
      this.metrics.keyspaceMisses = parseInt(stats.keyspace_misses || '0');
      this.metrics.evictedKeys = parseInt(stats.evicted_keys || '0');
      this.metrics.memoryUsage = parseInt(memoryInfo.used_memory || '0');
      this.metrics.connectedClients = parseInt(clientsInfo.connected_clients || '0');

      // Calculate hit rate
      const totalKeyspaceOps = this.metrics.keyspaceHits + this.metrics.keyspaceMisses;
      if (totalKeyspaceOps > 0) {
        const hitRate = (this.metrics.keyspaceHits / totalKeyspaceOps) * 100;
        this.metrics.hits = hitRate;
        this.metrics.misses = 100 - hitRate;
      }

    } catch (error) {
      logger.warn('Failed to collect Redis metrics:', error);
    }
  }

  /**
   * Parse Redis INFO command output
   */
  private parseRedisInfo(info: string): Record<string, string> {
    const result: Record<string, string> = {};
    info.split('\r\n').forEach(line => {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        if (key && value !== undefined) {
          result[key] = value;
        }
      }
    });
    return result;
  }

  /**
   * Get active Redis client (cluster or standalone) - NEVER returns subscriber client
   */
  private getActiveClient(): Redis.Redis | Cluster | null {
    // CRITICAL: Always return main client, never subscriber
    if (this.redisCluster) {
      return this.redisCluster;
    }

    // CRITICAL FIX: Ensure we NEVER return subscriber client
    if (this.redisClient) {
      // Double-check that this is not the subscriber client
      if (this.redisClient === this.subscriberClient) {
        logger.error('❌ CRITICAL: Main Redis client is subscriber client! This should never happen!');
        // Force recreate main client
        this.recreateMainClient();
        return this.redisClient;
      }
      return this.redisClient;
    }

    return null;
  }

  /**
   * Emergency method to recreate main client if it gets corrupted
   */
  private recreateMainClient(): void {
    try {
      logger.warn('🔧 Recreating main Redis client due to subscriber mode corruption...');

      if (this.redisClient && this.redisClient !== this.subscriberClient) {
        // Main client is fine, no need to recreate
        return;
      }

      // Recreate main client with same options as original
      const redisOptions = {
        ...this.configuration.cluster.options.redisOptions,
        keyPrefix: this.configuration.performance.keyPrefix,
        enableReadyCheck: true,
        lazyConnect: false,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        connectTimeout: 30000,
        commandTimeout: 10000
      };

      this.redisClient = new Redis(this.configuration.url, redisOptions);
      logger.info('✅ Main Redis client recreated successfully');
    } catch (error) {
      logger.error('❌ Failed to recreate main Redis client:', error);
    }
  }

  /**
   * Enterprise cache operations with intelligent routing
   */
  async set(key: string, value: any, ttl?: number): Promise<boolean> {
    const span = this.tracer.startSpan('redis_set', {
      kind: SpanKind.CLIENT,
      attributes: {
        'redis.key': key,
        'redis.ttl': ttl || 0
      }
    });

    const startTime = Date.now();

    try {
      const client = this.getActiveClient();
      if (!client) {
        throw new Error('No Redis client available');
      }

      const serializedValue = JSON.stringify(value);
      const fullKey = this.configuration.performance.keyPrefix + key;

      let result: string | null;
      if (ttl) {
        result = await client.setex(fullKey, ttl, serializedValue);
      } else {
        result = await client.set(fullKey, serializedValue);
      }

      this.metrics.operations++;
      const responseTime = Date.now() - startTime;
      this.updateAverageResponseTime(responseTime);

      span.setAttributes({
        'redis.success': result === 'OK',
        'redis.response_time': responseTime
      });
      span.setStatus({ code: SpanStatusCode.OK });

      return result === 'OK';

    } catch (error) {
      this.metrics.errors++;
      span.recordException(error as Error);
      span.setStatus({ code: SpanStatusCode.ERROR, message: (error as Error).message });
      logger.error('Redis SET operation failed:', error);
      return false;
    } finally {
      span.end();
    }
  }

  /**
   * Enterprise get operation with metrics tracking
   */
  async get<T>(key: string): Promise<T | null> {
    const span = this.tracer.startSpan('redis_get', {
      kind: SpanKind.CLIENT,
      attributes: {
        'redis.key': key
      }
    });

    const startTime = Date.now();

    try {
      const client = this.getActiveClient();
      if (!client) {
        throw new Error('No Redis client available');
      }

      const fullKey = this.configuration.performance.keyPrefix + key;
      const result = await client.get(fullKey);

      this.metrics.operations++;
      const responseTime = Date.now() - startTime;
      this.updateAverageResponseTime(responseTime);

      if (result) {
        this.metrics.hits++;
        span.setAttributes({ 'redis.hit': true });
        return JSON.parse(result);
      } else {
        this.metrics.misses++;
        span.setAttributes({ 'redis.hit': false });
        return null;
      }

    } catch (error) {
      this.metrics.errors++;
      span.recordException(error as Error);
      span.setStatus({ code: SpanStatusCode.ERROR, message: (error as Error).message });
      logger.error('Redis GET operation failed:', error);
      return null;
    } finally {
      span.end();
    }
  }

  /**
   * Update average response time
   */
  private updateAverageResponseTime(responseTime: number): void {
    if (this.metrics.operations === 1) {
      this.metrics.averageResponseTime = responseTime;
    } else {
      this.metrics.averageResponseTime =
        (this.metrics.averageResponseTime * (this.metrics.operations - 1) + responseTime) / this.metrics.operations;
    }
  }

  /**
   * Delete key with metrics tracking
   */
  async del(key: string): Promise<boolean> {
    const span = this.tracer.startSpan('redis_del', {
      kind: SpanKind.CLIENT,
      attributes: { 'redis.key': key }
    });

    try {
      const client = this.getActiveClient();
      if (!client) {
        throw new Error('No Redis client available');
      }

      const fullKey = this.configuration.performance.keyPrefix + key;
      const result = await client.del(fullKey);

      this.metrics.operations++;
      span.setStatus({ code: SpanStatusCode.OK });

      return result > 0;

    } catch (error) {
      this.metrics.errors++;
      span.recordException(error as Error);
      span.setStatus({ code: SpanStatusCode.ERROR, message: (error as Error).message });
      logger.error('Redis DEL operation failed:', error);
      return false;
    } finally {
      span.end();
    }
  }

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const client = this.getActiveClient();
      if (!client) return false;

      const fullKey = this.configuration.performance.keyPrefix + key;
      const result = await client.exists(fullKey);

      this.metrics.operations++;
      return result > 0;

    } catch (error) {
      this.metrics.errors++;
      logger.error('Redis EXISTS operation failed:', error);
      return false;
    }
  }

  /**
   * Set expiration on key
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const client = this.getActiveClient();
      if (!client) return false;

      const fullKey = this.configuration.performance.keyPrefix + key;
      const result = await client.expire(fullKey, seconds);

      this.metrics.operations++;
      return result === 1;

    } catch (error) {
      this.metrics.errors++;
      logger.error('Redis EXPIRE operation failed:', error);
      return false;
    }
  }

  /**
   * Get multiple keys at once (pipeline operation)
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    const span = this.tracer.startSpan('redis_mget', {
      kind: SpanKind.CLIENT,
      attributes: {
        'redis.keys_count': keys.length
      }
    });

    try {
      const client = this.getActiveClient();
      if (!client) {
        throw new Error('No Redis client available');
      }

      const fullKeys = keys.map(key => this.configuration.performance.keyPrefix + key);
      const results = await client.mget(...fullKeys);

      this.metrics.operations++;
      span.setStatus({ code: SpanStatusCode.OK });

      return results.map((result: any) => result ? JSON.parse(result) : null);

    } catch (error) {
      this.metrics.errors++;
      span.recordException(error as Error);
      span.setStatus({ code: SpanStatusCode.ERROR, message: (error as Error).message });
      logger.error('Redis MGET operation failed:', error);
      return keys.map(() => null);
    } finally {
      span.end();
    }
  }

  /**
   * Get Redis metrics
   */
  getMetrics(): RedisMetrics {
    return { ...this.metrics };
  }

  /**
   * Get Redis configuration
   */
  getConfiguration(): RedisConfiguration {
    return { ...this.configuration };
  }

  /**
   * Check if Redis is connected
   */
  isConnected(): boolean {
    try {
      // If Redis is disabled, consider it "connected" (no-op mode)
      if (process.env.DISABLE_REDIS === 'true') {
        return true;
      }

      const client = this.getActiveClient();
      if (!client) return false;

      // Check connection status
      if (this.redisCluster) {
        return this.redisCluster.status === 'ready';
      } else if (this.redisClient) {
        return this.redisClient.status === 'ready';
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Ping Redis to check connectivity
   */
  async ping(): Promise<boolean> {
    try {
      // If Redis is disabled, consider ping successful (no-op mode)
      if (process.env.DISABLE_REDIS === 'true') {
        return true;
      }

      const client = this.getActiveClient();
      if (!client) return false;

      await client.ping();
      return true;
    } catch (error) {
      logger.warn('Redis ping failed:', error);
      return false;
    }
  }

  /**
   * Check if Redis is healthy
   */
  async isHealthy(): Promise<boolean> {
    return await this.ping();
  }

  /**
   * Get Redis client for advanced operations (regular operations only)
   * CRITICAL: This method ensures we never return a subscriber client
   */
  getClient(): Redis.Redis | Cluster | null {
    // If Redis is disabled, return null
    if (process.env.DISABLE_REDIS === 'true') {
      return null;
    }

    const client = this.getActiveClient();

    // SAFEGUARD: Ensure we're not returning a subscriber client
    if (client === this.subscriberClient) {
      logger.error('❌ CRITICAL: getClient() attempted to return subscriber client, returning main client instead');
      return this.redisClient;
    }

    // ULTIMATE FIX: Check if the main client is in subscriber mode and recreate if needed
    if (client && this.isClientInSubscriberMode(client)) {
      logger.error('❌ CRITICAL: Main Redis client is in subscriber mode! Recreating...');
      this.recreateMainClientImmediate();
      return this.redisClient;
    }

    return client;
  }

  /**
   * Check if a Redis client is in subscriber mode
   */
  private isClientInSubscriberMode(client: Redis.Redis | Cluster): boolean {
    try {
      // For ioredis, check if the client has active subscriptions
      if ('mode' in client && client.mode === 'subscriber') {
        return true;
      }

      // Alternative check: try to execute a regular command
      // If it fails with subscriber mode error, we know it's in subscriber mode
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Immediately recreate main client if corrupted
   */
  private recreateMainClientImmediate(): void {
    try {
      logger.warn('🔧 EMERGENCY: Recreating main Redis client due to subscriber mode corruption...');

      // Disconnect the corrupted client
      if (this.redisClient && this.redisClient !== this.subscriberClient) {
        this.redisClient.disconnect();
      }

      // Create a fresh main client
      const redisOptions = {
        ...this.configuration.cluster.options.redisOptions,
        keyPrefix: this.configuration.performance.keyPrefix,
        enableReadyCheck: true,
        lazyConnect: false,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        connectTimeout: 30000,
        commandTimeout: 10000
      };

      this.redisClient = new Redis(this.configuration.url, redisOptions);

      logger.info('✅ EMERGENCY: Main Redis client recreated successfully');
    } catch (error) {
      logger.error('❌ EMERGENCY: Failed to recreate main Redis client:', error);
    }
  }

  /**
   * Get dedicated subscriber client (for pub/sub operations)
   */
  getSubscriberClient(): Redis.Redis | null {
    if (process.env.DISABLE_REDIS === 'true') {
      return null;
    }
    return this.subscriberClient;
  }

  /**
   * Get dedicated publisher client (for pub/sub operations)
   */
  getPublisherClient(): Redis.Redis | null {
    if (process.env.DISABLE_REDIS === 'true') {
      return null;
    }
    return this.publisherClient;
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    logger.info('🔄 Shutting down Enterprise Redis Manager...');

    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }

    const shutdownTasks = [];

    if (this.redisClient) {
      shutdownTasks.push(this.redisClient.disconnect());
    }

    if (this.subscriberClient) {
      shutdownTasks.push(this.subscriberClient.disconnect());
    }

    if (this.publisherClient) {
      shutdownTasks.push(this.publisherClient.disconnect());
    }

    if (this.redisCluster) {
      shutdownTasks.push(this.redisCluster.disconnect());
    }

    await Promise.allSettled(shutdownTasks);

    this.isInitialized = false;
    logger.info('✅ Enterprise Redis Manager shutdown completed');
  }
}

// Export singleton instance and backward compatibility functions
export const enterpriseRedisManager = EnterpriseRedisManager.getInstance();

// Backward compatibility exports
export const createRedisClient = () => {
  logger.warn('createRedisClient is deprecated. Use enterpriseRedisManager instead.');

  // Return null if Redis is disabled
  if (process.env.DISABLE_REDIS === 'true') {
    return null;
  }

  // Return the client only if it's already connected
  const client = enterpriseRedisManager.getClient();
  if (client) {
    return client;
  }

  // Return null if not connected yet, let the caller handle fallback
  return null;
};

export const connectRedis = async (): Promise<void> => {
  await enterpriseRedisManager.initialize();
};

export const getRedisClient = () => {
  return enterpriseRedisManager.getClient();
};

export const getRedisClientStrict = () => {
  const client = enterpriseRedisManager.getClient();
  if (!client) {
    throw new Error('Redis client not initialized. Call connectRedis() first.');
  }
  return client;
};

export const closeRedisConnection = async (): Promise<void> => {
  await enterpriseRedisManager.shutdown();
};

/**
 * Enterprise Cache Service - Enhanced version using Enterprise Redis Manager
 * Provides backward compatibility while leveraging enterprise features
 */
export class CacheService {
  private memoryCache = new Map<string, { value: any; expires: number }>();
  private redisManager = enterpriseRedisManager;

  constructor() {
    // Enterprise Redis Manager handles initialization
  }

  /**
   * Set cache with TTL and enterprise features
   */
  async set(key: string, value: any, ttlSeconds: number = 3600): Promise<void> {
    try {
      // Try enterprise Redis first
      const success = await this.redisManager.set(key, value, ttlSeconds);

      if (success) {
        logger.debug(`Enterprise cache set: ${key} (TTL: ${ttlSeconds}s)`);
        return;
      }

      // Fallback to memory cache
      const expires = Date.now() + (ttlSeconds * 1000);
      this.memoryCache.set(key, { value, expires });
      logger.debug(`Memory cache fallback set: ${key} (TTL: ${ttlSeconds}s)`);

    } catch (error) {
      logger.error(`Cache set error for key ${key}:`, error);
      // Fallback to memory cache on any error
      const expires = Date.now() + (ttlSeconds * 1000);
      this.memoryCache.set(key, { value, expires });
      logger.debug(`Memory cache error fallback for key: ${key}`);
    }
  }

  /**
   * Get cache with enterprise features and memory fallback
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      // Try enterprise Redis first
      const value = await this.redisManager.get<T>(key);

      if (value !== null) {
        logger.debug(`Enterprise cache hit: ${key}`);
        return value;
      }

      // Check memory cache fallback
      const cached = this.memoryCache.get(key);
      if (!cached) {
        logger.debug(`Cache miss: ${key}`);
        return null;
      }

      if (Date.now() > cached.expires) {
        this.memoryCache.delete(key);
        logger.debug(`Memory cache expired: ${key}`);
        return null;
      }

      logger.debug(`Memory cache hit: ${key}`);
      return cached.value as T;

    } catch (error) {
      logger.error(`Cache get error for key ${key}:`, error);
      // Fallback to memory cache on any error
      const cached = this.memoryCache.get(key);
      if (cached && Date.now() <= cached.expires) {
        return cached.value as T;
      }
      return null;
    }
  }

  /**
   * Delete cache with enterprise features
   */
  async delete(key: string): Promise<void> {
    try {
      await this.redisManager.del(key);
      this.memoryCache.delete(key); // Also remove from memory cache
      logger.debug(`Enterprise cache deleted: ${key}`);
    } catch (error) {
      logger.error(`Cache delete error for key ${key}:`, error);
      // Still try to remove from memory cache
      this.memoryCache.delete(key);
    }
  }

  /**
   * Check if key exists with enterprise features
   */
  async exists(key: string): Promise<boolean> {
    try {
      const exists = await this.redisManager.exists(key);
      if (exists) return true;

      // Check memory cache fallback
      const cached = this.memoryCache.get(key);
      return cached ? Date.now() <= cached.expires : false;
    } catch (error) {
      logger.error(`Cache exists check error for key ${key}:`, error);
      // Check memory cache fallback
      const cached = this.memoryCache.get(key);
      return cached ? Date.now() <= cached.expires : false;
    }
  }

  /**
   * Set TTL for existing key with enterprise features
   */
  async expire(key: string, ttlSeconds: number): Promise<void> {
    try {
      await this.redisManager.expire(key, ttlSeconds);

      // Update memory cache TTL if exists
      const cached = this.memoryCache.get(key);
      if (cached) {
        cached.expires = Date.now() + (ttlSeconds * 1000);
        this.memoryCache.set(key, cached);
      }

      logger.debug(`Enterprise cache TTL set: ${key} (${ttlSeconds}s)`);
    } catch (error) {
      logger.error(`Cache expire error for key ${key}:`, error);
    }
  }

  /**
   * Get multiple keys with enterprise features
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      const values = await this.redisManager.mget<T>(keys);
      logger.debug(`Enterprise cache mget: ${keys.length} keys`);
      return values;
    } catch (error) {
      logger.error(`Cache mget error for keys ${keys.join(', ')}:`, error);

      // Fallback to individual memory cache lookups
      return keys.map(key => {
        const cached = this.memoryCache.get(key);
        if (cached && Date.now() <= cached.expires) {
          return cached.value as T;
        }
        return null;
      });
    }
  }

  /**
   * Set multiple keys with enterprise features
   */
  async mset(keyValuePairs: Record<string, any>, ttlSeconds: number = 3600): Promise<void> {
    try {
      // Use enterprise Redis for bulk operations
      const client = this.redisManager.getClient();
      if (!client) {
        // Fallback to individual memory cache sets
        for (const [key, value] of Object.entries(keyValuePairs)) {
          const expires = Date.now() + (ttlSeconds * 1000);
          this.memoryCache.set(key, { value, expires });
        }
        logger.debug(`Memory cache mset: ${Object.keys(keyValuePairs).join(', ')} (TTL: ${ttlSeconds}s)`);
        return;
      }

      // Use pipeline for efficient bulk operations
      const pipeline = client.pipeline();

      for (const [key, value] of Object.entries(keyValuePairs)) {
        const fullKey = this.redisManager.getConfiguration().performance.keyPrefix + key;
        const serializedValue = JSON.stringify(value);
        pipeline.setex(fullKey, ttlSeconds, serializedValue);
      }

      await pipeline.exec();
      logger.debug(`Enterprise cache mset: ${Object.keys(keyValuePairs).join(', ')} (TTL: ${ttlSeconds}s)`);
    } catch (error) {
      logger.error(`Cache mset error:`, error);
      // Fallback to memory cache
      for (const [key, value] of Object.entries(keyValuePairs)) {
        const expires = Date.now() + (ttlSeconds * 1000);
        this.memoryCache.set(key, { value, expires });
      }
    }
  }

  /**
   * Get cache metrics from enterprise Redis manager
   */
  getMetrics() {
    return this.redisManager.getMetrics();
  }

  /**
   * Check if Redis is healthy
   */
  async isHealthy(): Promise<boolean> {
    return await this.redisManager.isHealthy();
  }

  /**
   * Clear memory cache (for testing/debugging)
   */
  clearMemoryCache(): void {
    this.memoryCache.clear();
    logger.debug('Memory cache cleared');
  }

}

// Export singleton instance for backward compatibility
export const cacheService = new CacheService();

// Default export for backward compatibility
export default enterpriseRedisManager;
