# Database Configuration
DATABASE_URL="postgresql://postgres:password@localhost:5432/x_marketing"
REDIS_URL="redis://localhost:6379"

# X API Configuration (Twitter)
X_API_KEY="your_x_api_key_here"
X_API_SECRET="your_x_api_secret_here"
X_BEARER_TOKEN="your_x_bearer_token_here"
X_ACCESS_TOKEN="your_x_access_token_here"
X_ACCESS_TOKEN_SECRET="your_x_access_token_secret_here"

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN="your_telegram_bot_token_here"
TELEGRAM_WEBHOOK_URL="https://your-domain.com/webhook"

# LLM Service Configuration
OLLAMA_HOST="http://localhost:11434"
HUGGINGFACE_API_KEY="your_huggingface_api_key_here"
OPENAI_API_KEY="your_openai_api_key_here"  # Optional fallback

# Security Configuration
JWT_SECRET="your_super_secret_jwt_key_here_min_32_chars"
JWT_REFRESH_SECRET="your_super_secret_refresh_key_here_min_32_chars"
ENCRYPTION_KEY="your_32_character_encryption_key_here"
BCRYPT_ROUNDS=12

# Basic Proxy Configuration (Legacy)
PROXY_PROVIDER="your_proxy_provider"  # brightdata, smartproxy, etc.
PROXY_USERNAME="your_proxy_username"
PROXY_PASSWORD="your_proxy_password"
PROXY_ENDPOINT="your_proxy_endpoint"
PROXY_PORT=8080

# Twikit Enterprise Proxy Configuration
TWIKIT_ENABLE_PROXY_ROTATION=true
TWIKIT_PROXY_ROTATION_INTERVAL=300
TWIKIT_PROXY_HEALTH_CHECK_INTERVAL=60
TWIKIT_PROXY_MAX_FAILURES=5
TWIKIT_PROXY_HEALTH_CHECK_TIMEOUT=10

# Residential Proxy Pool
TWIKIT_RESIDENTIAL_PROXY_ENABLED=false
TWIKIT_RESIDENTIAL_PROXY_URLS=""
TWIKIT_RESIDENTIAL_PROXY_USERNAME=""
TWIKIT_RESIDENTIAL_PROXY_PASSWORD=""

# Datacenter Proxy Pool
TWIKIT_DATACENTER_PROXY_ENABLED=false
TWIKIT_DATACENTER_PROXY_URLS=""
TWIKIT_DATACENTER_PROXY_USERNAME=""
TWIKIT_DATACENTER_PROXY_PASSWORD=""

# Mobile Proxy Pool
TWIKIT_MOBILE_PROXY_ENABLED=false
TWIKIT_MOBILE_PROXY_URLS=""
TWIKIT_MOBILE_PROXY_USERNAME=""
TWIKIT_MOBILE_PROXY_PASSWORD=""

# Proxy Health Check Endpoints
TWIKIT_PROXY_HEALTH_CHECK_URLS="https://httpbin.org/ip,https://api.ipify.org?format=json,https://ifconfig.me/ip"

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
X_API_RATE_LIMIT_BUFFER=0.8  # Use 80% of rate limit

# Content Generation Configuration
DEFAULT_CONTENT_LANGUAGE="en"
MAX_CONTENT_LENGTH=280
CONTENT_APPROVAL_REQUIRED=true
AI_CONTENT_DISCLAIMER=true

# Analytics Configuration
ANALYTICS_RETENTION_DAYS=90
METRICS_UPDATE_INTERVAL=300000  # 5 minutes

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your_app_password"

# Application Configuration
NODE_ENV="development"
PORT=3001
FRONTEND_URL="http://localhost:3000"
BACKEND_URL="http://localhost:3001"
TELEGRAM_BOT_URL="http://localhost:3002"
LLM_SERVICE_URL="http://localhost:3003"

# Logging Configuration
LOG_LEVEL="info"
LOG_FILE_PATH="./logs/app.log"
LOG_MAX_SIZE="10m"
LOG_MAX_FILES="5"

# Feature Flags
ENABLE_ACCOUNT_CREATION=false  # Disable by default for safety
ENABLE_AUTO_FOLLOW=true
ENABLE_AUTO_LIKE=true
ENABLE_AUTO_RETWEET=true
ENABLE_AUTO_REPLY=true
ENABLE_CONTENT_GENERATION=true

# Safety Configuration
MAX_ACCOUNTS_PER_USER=10
MAX_DAILY_ACTIONS_PER_ACCOUNT=100
MIN_ACTION_DELAY_MS=5000  # 5 seconds minimum between actions
MAX_ACTION_DELAY_MS=30000  # 30 seconds maximum between actions

# Monitoring Configuration
HEALTH_CHECK_INTERVAL=60000  # 1 minute
ACCOUNT_HEALTH_CHECK_INTERVAL=300000  # 5 minutes
METRICS_COLLECTION_ENABLED=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH="./backups"

# Development Configuration
DEBUG_MODE=false
MOCK_X_API=false  # Use mock responses for development
SKIP_RATE_LIMITING=false  # Only for development
