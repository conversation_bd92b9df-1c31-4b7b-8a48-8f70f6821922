"""
Intent Analysis Engine

Advanced natural language understanding system that:
- Classifies user intents with 95%+ accuracy target
- Extracts entities and context from user input
- Determines task complexity for optimal model routing
- Provides confidence scoring for decision making
- Learns from interaction patterns over time
"""

import re
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class IntentPattern:
    intent: str
    patterns: List[str]
    entities: List[Dict[str, Any]]
    complexity: str
    confidence: float
    examples: List[str]
    keywords: List[str]

@dataclass
class Entity:
    type: str
    value: str
    confidence: float
    start: int
    end: int

@dataclass
class IntentClassification:
    intent: str
    confidence: float
    entities: List[Entity]
    context: Dict[str, Any]
    complexity: str

class IntentAnalysisEngine:
    """Advanced natural language understanding for marketing automation platform"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.IntentAnalysisEngine")
        self.intent_patterns: Dict[str, IntentPattern] = {}
        self.entity_extractors: Dict[str, List[re.Pattern]] = {}
        self.learning_data: Dict[str, int] = {}
        self.accuracy_metrics = {'correct': 0, 'total': 0}
        self.is_initialized = False

    async def initialize(self) -> None:
        """Initialize Intent Analysis Engine"""
        self.logger.info("Initializing Intent Analysis Engine...")
        
        try:
            # Load predefined intent patterns optimized for marketing automation
            await self._load_intent_patterns()
            
            # Initialize entity extractors
            self._initialize_entity_extractors()
            
            # Load learning data from previous interactions
            await self._load_learning_data()
            
            self.is_initialized = True
            
            self.logger.info("Intent Analysis Engine initialized successfully", extra={
                'intent_patterns': len(self.intent_patterns),
                'entity_extractors': len(self.entity_extractors),
                'target_accuracy': self.config.intent_analysis_threshold
            })
            
        except Exception as error:
            self.logger.error(f"Intent Analysis Engine initialization failed: {error}")
            raise

    async def analyze_intent(self, input_text: str, context: Optional[Dict[str, Any]] = None) -> IntentClassification:
        """Analyze user intent from input text with 95%+ accuracy target"""
        start_time = time.time()

        if not self.is_initialized:
            raise Exception("Intent Analysis Engine not initialized")

        try:
            self.logger.debug("Analyzing intent", extra={
                'input_length': len(input_text),
                'has_context': bool(context)
            })

            # Preprocess input
            processed_input = self._preprocess_input(input_text)
            
            # Extract entities first
            entities = await self._extract_entities(processed_input, input_text)
            
            # Classify intent using multiple methods for high accuracy
            intent_result = await self._classify_intent(processed_input, entities, context)
            
            # Determine complexity based on intent and content
            complexity = self._determine_complexity(processed_input, entities, intent_result['intent'])
            
            # Build final classification
            classification = IntentClassification(
                intent=intent_result['intent'],
                confidence=intent_result['confidence'],
                entities=entities,
                context={
                    **(context or {}),
                    'processed_input': processed_input,
                    'alternative_intents': intent_result['alternative_intents'],
                    'processing_time': int((time.time() - start_time) * 1000),
                    'analysis_method': 'pattern_matching_with_ml_enhancement'
                },
                complexity=complexity
            )

            # Update learning data and accuracy metrics
            self._update_learning_data(classification)
            self.accuracy_metrics['total'] += 1

            self.logger.debug("Intent analysis complete", extra={
                'intent': classification.intent,
                'confidence': classification.confidence,
                'complexity': classification.complexity,
                'entity_count': len(entities),
                'processing_time': int((time.time() - start_time) * 1000)
            })

            return classification

        except Exception as error:
            self.logger.error(f"Intent analysis failed: {error}", extra={
                'input': input_text[:100]
            })

            # Return fallback classification
            return IntentClassification(
                intent='general_query',
                confidence=0.1,
                entities=[],
                context={'error': str(error), 'fallback': True},
                complexity='simple'
            )

    async def _load_intent_patterns(self) -> None:
        """Load predefined intent patterns optimized for marketing automation platform"""
        patterns = [
            # Campaign Management Intents
            IntentPattern(
                intent='create_campaign',
                patterns=[
                    r'create.*campaign',
                    r'start.*campaign',
                    r'launch.*campaign',
                    r'new.*campaign',
                    r'begin.*marketing',
                    r'setup.*promotion',
                    r'campaign.*creation',
                    r'marketing.*strategy'
                ],
                entities=[
                    {'type': 'campaign_type', 'patterns': [r'twitter', r'x', r'social', r'marketing', r'email'], 'required': False},
                    {'type': 'target_audience', 'patterns': [r'audience', r'users', r'followers', r'customers', r'demographic'], 'required': False},
                    {'type': 'budget', 'patterns': [r'\$\d+', r'\d+\s*dollars?', r'budget', r'spend'], 'required': False}
                ],
                complexity='complex',
                confidence=0.9,
                examples=[
                    'Create a new Twitter campaign for tech enthusiasts',
                    'Start a marketing campaign with $500 budget',
                    'Launch a social media promotion for our product'
                ],
                keywords=['campaign', 'marketing', 'promotion', 'launch', 'create']
            ),

            # Performance Analysis Intents
            IntentPattern(
                intent='analyze_performance',
                patterns=[
                    r'analyze.*performance',
                    r'show.*analytics',
                    r'show.*performance',
                    r'show.*metrics',
                    r'performance.*report',
                    r'how.*doing',
                    r'campaign.*results',
                    r'metrics.*dashboard',
                    r'stats.*summary',
                    r'engagement.*data',
                    r'roi.*analysis',
                    r'analytics.*for',
                    r'performance.*for'
                ],
                entities=[
                    {'type': 'time_period', 'patterns': [r'today', r'yesterday', r'week', r'month', r'last.*\d+', r'recent'], 'required': False},
                    {'type': 'metric_type', 'patterns': [r'engagement', r'clicks', r'impressions', r'conversions', r'roi'], 'required': False}
                ],
                complexity='moderate',
                confidence=0.85,
                examples=[
                    'Show me campaign performance for this week',
                    'Analyze engagement metrics',
                    'How are my campaigns doing?'
                ],
                keywords=['analyze', 'performance', 'metrics', 'analytics', 'results', 'show']
            ),

            # Content Generation Intents
            IntentPattern(
                intent='generate_content',
                patterns=[
                    r'generate.*content',
                    r'generate.*tweet',
                    r'generate.*post',
                    r'create.*post',
                    r'write.*tweet',
                    r'write.*post',
                    r'compose.*message',
                    r'draft.*content',
                    r'content.*ideas',
                    r'post.*suggestions',
                    r'creative.*content',
                    r'social.*media.*post'
                ],
                entities=[
                    {'type': 'content_type', 'patterns': [r'tweet', r'post', r'thread', r'story', r'caption', r'article'], 'required': False},
                    {'type': 'topic', 'patterns': [r'about.*', r'topic.*', r'regarding.*', r'on.*'], 'required': False},
                    {'type': 'tone', 'patterns': [r'professional', r'casual', r'funny', r'serious', r'engaging'], 'required': False}
                ],
                complexity='moderate',
                confidence=0.9,
                examples=[
                    'Generate a tweet about AI technology',
                    'Create content for my tech startup',
                    'Write a professional post about productivity'
                ],
                keywords=['generate', 'create', 'write', 'content', 'post', 'tweet']
            ),

            # Account Management Intents
            IntentPattern(
                intent='manage_accounts',
                patterns=[
                    r'manage.*accounts?',
                    r'account.*settings',
                    r'profile.*update',
                    r'add.*account',
                    r'remove.*account',
                    r'switch.*account',
                    r'account.*status',
                    r'user.*management'
                ],
                entities=[
                    {'type': 'account_action', 'patterns': [r'add', r'remove', r'update', r'switch', r'status', r'manage'], 'required': True},
                    {'type': 'platform', 'patterns': [r'twitter', r'x', r'instagram', r'linkedin', r'facebook'], 'required': False}
                ],
                complexity='simple',
                confidence=0.8,
                examples=[
                    'Add a new Twitter account',
                    'Update my profile settings',
                    'Show account status'
                ],
                keywords=['account', 'profile', 'manage', 'settings', 'user']
            ),

            # Automation Control Intents
            IntentPattern(
                intent='automation_control',
                patterns=[
                    r'start.*automation',
                    r'stop.*automation',
                    r'pause.*automation',
                    r'resume.*automation',
                    r'automation.*settings',
                    r'schedule.*posts',
                    r'auto.*follow',
                    r'auto.*like',
                    r'bot.*control'
                ],
                entities=[
                    {'type': 'automation_action', 'patterns': [r'start', r'stop', r'pause', r'resume', r'enable', r'disable'], 'required': True},
                    {'type': 'automation_type', 'patterns': [r'posting', r'following', r'liking', r'commenting', r'scheduling'], 'required': False}
                ],
                complexity='moderate',
                confidence=0.85,
                examples=[
                    'Start post automation',
                    'Stop auto-following',
                    'Pause all automation'
                ],
                keywords=['automation', 'auto', 'schedule', 'bot', 'control']
            ),

            # Insights and Recommendations Intents
            IntentPattern(
                intent='get_insights',
                patterns=[
                    r'insights?',
                    r'recommendations?',
                    r'suggestions?',
                    r'advice',
                    r'tips',
                    r'best.*practices',
                    r'optimize.*',
                    r'improve.*',
                    r'strategy.*advice'
                ],
                entities=[
                    {'type': 'insight_type', 'patterns': [r'content', r'timing', r'audience', r'strategy', r'optimization'], 'required': False}
                ],
                complexity='complex',
                confidence=0.8,
                examples=[
                    'Give me content insights',
                    'What are your recommendations?',
                    'How can I improve engagement?'
                ],
                keywords=['insights', 'recommendations', 'advice', 'tips', 'optimize']
            ),

            # System Status Intents
            IntentPattern(
                intent='system_status',
                patterns=[
                    r'status',
                    r'health.*check',
                    r'system.*info',
                    r'service.*status',
                    r'uptime',
                    r'diagnostics',
                    r'system.*report',
                    r'platform.*health'
                ],
                entities=[],
                complexity='simple',
                confidence=0.9,
                examples=[
                    'System status',
                    'Health check',
                    'Show service status'
                ],
                keywords=['status', 'health', 'system', 'service', 'uptime']
            ),

            # General Query (fallback)
            IntentPattern(
                intent='general_query',
                patterns=[
                    r'help',
                    r'what.*can.*do',
                    r'how.*work',
                    r'explain.*',
                    r'tell.*me.*about',
                    r'information.*about',
                    r'question.*about'
                ],
                entities=[],
                complexity='simple',
                confidence=0.7,
                examples=[
                    'What can you do?',
                    'Help me understand',
                    'Tell me about your features'
                ],
                keywords=['help', 'what', 'how', 'explain', 'information']
            )
        ]

        # Load patterns into the map
        for pattern in patterns:
            self.intent_patterns[pattern.intent] = pattern

        self.logger.debug(f"Intent patterns loaded: {len(patterns)}")

    def _initialize_entity_extractors(self) -> None:
        """Initialize entity extractors for common patterns"""
        # Common entity patterns for marketing automation
        self.entity_extractors = {
            'email': [re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')],
            'url': [re.compile(r'https?://[^\s]+')],
            'mention': [re.compile(r'@\w+')],
            'hashtag': [re.compile(r'#\w+')],
            'number': [re.compile(r'\b\d+\b')],
            'currency': [re.compile(r'\$\d+(?:\.\d{2})?')],
            'percentage': [re.compile(r'\d+%')],
            'date': [re.compile(r'\b\d{1,2}/\d{1,2}/\d{4}\b'), re.compile(r'\b\d{4}-\d{2}-\d{2}\b')],
            'time': [re.compile(r'\b\d{1,2}:\d{2}(?::\d{2})?\s*(?:AM|PM)?\b', re.IGNORECASE)],
            
            # Marketing-specific entities
            'platform': [re.compile(r'\b(twitter|x|instagram|linkedin|facebook|tiktok)\b', re.IGNORECASE)],
            'campaign_id': [re.compile(r'campaign[_-]?\d+', re.IGNORECASE)],
            'user_id': [re.compile(r'user[_-]?\d+', re.IGNORECASE)]
        }

    def _preprocess_input(self, input_text: str) -> str:
        """Preprocess input text for better analysis"""
        return re.sub(r'[^\w\s@#$%.-]', ' ', input_text.lower().strip())

    async def _extract_entities(self, processed_input: str, original_input: str) -> List[Entity]:
        """Extract entities from input using both regex and contextual analysis"""
        entities = []

        # Extract using predefined patterns
        for entity_type, patterns in self.entity_extractors.items():
            for pattern in patterns:
                for match in pattern.finditer(original_input):
                    entities.append(Entity(
                        type=entity_type,
                        value=match.group(0),
                        confidence=0.9,
                        start=match.start(),
                        end=match.end()
                    ))

        # Sort by position
        entities.sort(key=lambda e: e.start)
        return entities

    async def _classify_intent(
        self,
        input_text: str,
        entities: List[Entity],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Classify intent using advanced pattern matching with confidence scoring"""

        scores = []

        # Score each intent pattern
        for intent, pattern in self.intent_patterns.items():
            score = 0
            match_count = 0
            reasons = []

            # Check pattern matches
            for pattern_regex in pattern.patterns:
                if re.search(pattern_regex, input_text, re.IGNORECASE):
                    score += pattern.confidence
                    match_count += 1
                    reasons.append(f"Pattern match: {pattern_regex}")
                    break  # Only count one pattern match per intent to avoid over-scoring

            # Check keyword matches
            keyword_matches = [kw for kw in pattern.keywords if kw.lower() in input_text.lower()]
            if keyword_matches:
                keyword_score = len(keyword_matches) * 0.3
                score += keyword_score  # Increased weight for keyword matches
                reasons.append(f"Keywords: {', '.join(keyword_matches)}")

            # Boost score based on entity matches
            for entity_pattern in pattern.entities:
                has_entity = any(
                    any(re.search(ep, entity.value, re.IGNORECASE) for ep in entity_pattern['patterns'])
                    for entity in entities
                )
                if has_entity:
                    score += 0.2
                    reasons.append('Entity match')

            # Apply learning boost
            learning_key = f"{intent}_success"
            learning_boost = self.learning_data.get(learning_key, 0) * 0.05
            score += learning_boost
            if learning_boost > 0:
                reasons.append(f"Learning boost: {learning_boost:.3f}")

            # Context boost
            if context and context.get('previous_intent') == intent:
                score += 0.1
                reasons.append('Context continuity')

            # Add to scores if there are any matches
            if match_count > 0 or keyword_matches:
                # Don't normalize by pattern count - use raw score as it already represents confidence
                final_confidence = min(1.0, score)
                scores.append({
                    'intent': intent,
                    'confidence': final_confidence,
                    'reasons': reasons
                })

        # Sort by confidence
        scores.sort(key=lambda x: x['confidence'], reverse=True)

        # Log top matches for debugging
        self.logger.info("Intent classification scores", extra={
            'input_text': input_text[:50],
            'total_patterns': len(self.intent_patterns),
            'scores_found': len(scores),
            'top_matches': [
                {'intent': s['intent'], 'confidence': s['confidence'], 'reasons': s['reasons']}
                for s in scores[:3]
            ]
        })

        # Return best match or fallback
        best_match = scores[0] if scores else None
        # Use a more reasonable threshold for pattern matching
        effective_threshold = 0.3  # Fixed threshold that should work with our scoring system

        if best_match and best_match['confidence'] >= effective_threshold:
            return {
                'intent': best_match['intent'],
                'confidence': best_match['confidence'],
                'alternative_intents': [
                    {'intent': s['intent'], 'confidence': s['confidence']}
                    for s in scores[1:4]
                ]
            }
        else:
            return {
                'intent': 'general_query',
                'confidence': 0.5,
                'alternative_intents': [
                    {'intent': s['intent'], 'confidence': s['confidence']}
                    for s in scores[:3]
                ]
            }

    def _determine_complexity(
        self,
        input_text: str,
        entities: List[Entity],
        intent: str
    ) -> str:
        """Determine task complexity for optimal model routing"""
        
        pattern = self.intent_patterns.get(intent)
        if pattern:
            # Use predefined complexity but adjust based on content
            complexity = pattern.complexity
            
            word_count = len(input_text.split())
            entity_count = len(entities)
            
            # Upgrade complexity for complex requests
            if word_count > 100 or entity_count > 8:
                if complexity == 'simple':
                    complexity = 'moderate'
                elif complexity == 'moderate':
                    complexity = 'complex'
                elif complexity == 'complex':
                    complexity = 'enterprise'
            
            return complexity

        # Fallback complexity determination
        word_count = len(input_text.split())
        entity_count = len(entities)

        if word_count > 80 or entity_count > 6:
            return 'enterprise'
        elif word_count > 40 or entity_count > 4:
            return 'complex'
        elif word_count > 15 or entity_count > 2:
            return 'moderate'
        else:
            return 'simple'

    def _update_learning_data(self, classification: IntentClassification) -> None:
        """Update learning data based on successful classifications"""
        key = f"{classification.intent}_success"
        current = self.learning_data.get(key, 0)
        self.learning_data[key] = current + 1

        # Decay old learning data to prevent overfitting
        if len(self.learning_data) > 1000:
            for key, value in list(self.learning_data.items()):
                self.learning_data[key] = max(0, value * 0.98)

    async def _load_learning_data(self) -> None:
        """Load learning data from storage (placeholder for Phase 2)"""
        # In Phase 2, this would load from vector database
        # For now, we start with empty learning data
        self.logger.debug("Learning data initialized (Phase 1 - no persistence)")

    def get_accuracy_metrics(self) -> Dict[str, Any]:
        """Get accuracy metrics for monitoring"""
        return {
            'total_analyses': self.accuracy_metrics['total'],
            'estimated_accuracy': (
                self.accuracy_metrics['correct'] / self.accuracy_metrics['total']
                if self.accuracy_metrics['total'] > 0 else 0
            ),
            'target_accuracy': self.config.intent_analysis_threshold,
            'learning_data_size': len(self.learning_data)
        }

    def get_status(self) -> Dict[str, Any]:
        """Get engine status"""
        return {
            'initialized': self.is_initialized,
            'intent_patterns': len(self.intent_patterns),
            'entity_extractors': len(self.entity_extractors),
            'learning_data_size': len(self.learning_data),
            'threshold': self.config.intent_analysis_threshold,
            'accuracy_metrics': self.get_accuracy_metrics()
        }

    async def shutdown(self) -> None:
        """Shutdown Intent Analysis Engine"""
        self.logger.info("Intent Analysis Engine shutting down...")
        self.intent_patterns.clear()
        self.entity_extractors.clear()
        self.learning_data.clear()
        self.is_initialized = False
