/**
 * Raw IP Connection Test Script
 * 
 * Tests ScaleGrid connections using raw IP addresses (bypassing DNS)
 * Usage: node test-raw-ip-connections.js
 */

require('dotenv').config();
const { Pool } = require('pg');
const Redis = require('ioredis');

// Raw IP addresses from DNS resolution
const POSTGRESQL_IP = '************';
const POSTGRESQL_PORT = 6432;
const REDIS_IP = '***************';
const REDIS_PORT = 6379;

async function testPostgreSQLRawIP() {
  console.log('\n🐘 Testing PostgreSQL Raw IP Connection...');
  console.log(`IP: ${POSTGRESQL_IP}:${POSTGRESQL_PORT}`);
  console.log(`User: ${process.env.SCALEGRID_PG_USER}`);
  console.log(`Database: ${process.env.SCALEGRID_PG_DATABASE}`);
  
  try {
    const startTime = Date.now();
    
    const pool = new Pool({
      host: POSTGRESQL_IP, // Using raw IP instead of hostname
      port: POSTGRESQL_PORT,
      database: process.env.SCALEGRID_PG_DATABASE || 'postgres',
      user: process.env.SCALEGRID_PG_USER,
      password: process.env.SCALEGRID_PG_PASSWORD,
      ssl: false, // No SSL for new ScaleGrid instance
      connectionTimeoutMillis: 30000,
      query_timeout: 15000,
      max: 1
    });
    
    console.log('🔄 Attempting PostgreSQL connection to raw IP...');
    
    const client = await Promise.race([
      pool.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout after 30s')), 30000)
      )
    ]);
    
    console.log('🔄 Testing query...');
    const result = await client.query('SELECT 1 as test, version() as version');
    
    client.release();
    await pool.end();
    
    const latency = Date.now() - startTime;
    console.log(`✅ PostgreSQL raw IP connection successful (${latency}ms)`);
    console.log(`   Version: ${result.rows[0].version}`);
    
    return { success: true, latency, version: result.rows[0].version };
    
  } catch (error) {
    console.log(`❌ PostgreSQL raw IP connection failed: ${error.message}`);
    console.log(`   Error type: ${error.constructor.name}`);
    if (error.code) console.log(`   Error code: ${error.code}`);
    return { success: false, error: error.message, code: error.code };
  }
}

async function testRedisRawIP() {
  console.log('\n🔴 Testing Redis Raw IP Connection...');
  console.log(`IP: ${REDIS_IP}:${REDIS_PORT}`);
  
  try {
    const startTime = Date.now();
    
    const redis = new Redis({
      host: REDIS_IP, // Using raw IP instead of hostname
      port: REDIS_PORT,
      password: process.env.SCALEGRID_REDIS_PASSWORD,
      connectTimeout: 30000,
      commandTimeout: 15000,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });
    
    console.log('🔄 Attempting Redis connection to raw IP...');
    
    await Promise.race([
      redis.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout after 30s')), 30000)
      )
    ]);
    
    console.log('🔄 Testing ping...');
    const pong = await redis.ping();
    
    console.log('🔄 Testing info...');
    const info = await redis.info('server');
    
    await redis.disconnect();
    
    const latency = Date.now() - startTime;
    console.log(`✅ Redis raw IP connection successful (${latency}ms)`);
    console.log(`   Ping response: ${pong}`);
    
    return { success: true, latency, ping: pong };
    
  } catch (error) {
    console.log(`❌ Redis raw IP connection failed: ${error.message}`);
    console.log(`   Error type: ${error.constructor.name}`);
    if (error.code) console.log(`   Error code: ${error.code}`);
    return { success: false, error: error.message, code: error.code };
  }
}

async function main() {
  console.log('🚀 ScaleGrid Raw IP Connection Test Suite');
  console.log('==========================================');
  console.log(`PostgreSQL Target: ${POSTGRESQL_IP}:${POSTGRESQL_PORT}`);
  console.log(`Redis Target: ${REDIS_IP}:${REDIS_PORT}`);
  console.log('==========================================\n');
  
  const results = [];
  
  // Test PostgreSQL with raw IP
  results.push(await testPostgreSQLRawIP());
  
  // Test Redis with raw IP
  results.push(await testRedisRawIP());
  
  // Summary
  console.log('\n📊 Raw IP Test Summary');
  console.log('======================');
  
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`Total tests: ${total}`);
  console.log(`Successful: ${successful}`);
  console.log(`Failed: ${total - successful}`);
  
  if (successful === total) {
    console.log('\n🎉 All raw IP connections successful! Firewall allows IP access.');
    console.log('💡 Recommendation: Update configuration to use raw IPs instead of hostnames.');
    process.exit(0);
  } else if (successful > 0) {
    console.log('\n⚠️ Partial success. Some IPs are whitelisted, others may need firewall updates.');
    process.exit(1);
  } else {
    console.log('\n❌ All raw IP connections failed. Firewall is blocking these specific IPs.');
    console.log('💡 Recommendation: Whitelist these IPs in your firewall/network configuration.');
    process.exit(1);
  }
}

main().catch(error => {
  console.error('💥 Raw IP test suite crashed:', error);
  process.exit(1);
});
