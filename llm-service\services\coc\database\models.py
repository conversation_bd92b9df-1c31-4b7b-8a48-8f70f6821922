"""
Enhanced Platform Schema Generator - PostgreSQL Database Models

Database models for persisting Platform Schema Generator data including:
- Service schemas and endpoint definitions
- OpenAPI specification cache
- Discovery statistics and metrics
- Tool definitions and capability mappings
- Service health and availability data
"""

import asyncio
import asyncpg
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import asdict

logger = logging.getLogger(__name__)

class PlatformSchemaDB:
    """PostgreSQL database interface for Platform Schema Generator"""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.pool = None
        self.logger = logging.getLogger(f"{__name__}.PlatformSchemaDB")
    
    async def initialize(self) -> None:
        """Initialize database connection and create tables"""
        try:
            # Create connection pool
            self.pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=2,
                max_size=10,
                command_timeout=30
            )
            
            # Create tables
            await self._create_tables()
            
            self.logger.info("Platform Schema Database initialized successfully")
            
        except Exception as error:
            self.logger.error(f"Database initialization failed: {error}")
            raise
    
    async def _create_tables(self) -> None:
        """Create all required tables for Platform Schema Generator"""
        
        # Service Schemas table
        service_schemas_sql = """
        CREATE TABLE IF NOT EXISTS coc_service_schemas (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            version VARCHAR(50) NOT NULL,
            base_url VARCHAR(500) NOT NULL,
            discovery_method VARCHAR(50) DEFAULT 'manual',
            completeness_score DECIMAL(5,2) DEFAULT 0.0,
            openapi_spec JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_discovered TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_service_schemas_name ON coc_service_schemas(name);
        CREATE INDEX IF NOT EXISTS idx_service_schemas_discovery_method ON coc_service_schemas(discovery_method);
        """
        
        # Service Availability table
        service_availability_sql = """
        CREATE TABLE IF NOT EXISTS coc_service_availability (
            id SERIAL PRIMARY KEY,
            service_name VARCHAR(100) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'offline',
            uptime DECIMAL(5,2) DEFAULT 0.0,
            response_time DECIMAL(10,2) DEFAULT 0.0,
            error_rate DECIMAL(5,4) DEFAULT 0.0,
            health_score DECIMAL(5,2) DEFAULT 0.0,
            last_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (service_name) REFERENCES coc_service_schemas(name) ON DELETE CASCADE
        );
        
        CREATE INDEX IF NOT EXISTS idx_service_availability_service ON coc_service_availability(service_name);
        CREATE INDEX IF NOT EXISTS idx_service_availability_status ON coc_service_availability(status);
        """
        
        # Endpoint Schemas table
        endpoint_schemas_sql = """
        CREATE TABLE IF NOT EXISTS coc_endpoint_schemas (
            id SERIAL PRIMARY KEY,
            service_name VARCHAR(100) NOT NULL,
            path VARCHAR(500) NOT NULL,
            method VARCHAR(10) NOT NULL,
            description TEXT,
            tags JSONB DEFAULT '[]',
            complexity VARCHAR(20) DEFAULT 'moderate',
            estimated_response_time INTEGER DEFAULT 1000,
            risk_level VARCHAR(20) DEFAULT 'low',
            deprecated BOOLEAN DEFAULT FALSE,
            security_requirements JSONB DEFAULT '[]',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (service_name) REFERENCES coc_service_schemas(name) ON DELETE CASCADE,
            UNIQUE(service_name, path, method)
        );
        
        CREATE INDEX IF NOT EXISTS idx_endpoint_schemas_service ON coc_endpoint_schemas(service_name);
        CREATE INDEX IF NOT EXISTS idx_endpoint_schemas_method ON coc_endpoint_schemas(method);
        CREATE INDEX IF NOT EXISTS idx_endpoint_schemas_complexity ON coc_endpoint_schemas(complexity);
        CREATE INDEX IF NOT EXISTS idx_endpoint_schemas_risk ON coc_endpoint_schemas(risk_level);
        """
        
        # Endpoint Parameters table
        endpoint_parameters_sql = """
        CREATE TABLE IF NOT EXISTS coc_endpoint_parameters (
            id SERIAL PRIMARY KEY,
            endpoint_id INTEGER NOT NULL,
            name VARCHAR(100) NOT NULL,
            type VARCHAR(50) NOT NULL,
            required BOOLEAN DEFAULT FALSE,
            description TEXT,
            schema_definition JSONB,
            example_value JSONB,
            enum_values JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (endpoint_id) REFERENCES coc_endpoint_schemas(id) ON DELETE CASCADE
        );
        
        CREATE INDEX IF NOT EXISTS idx_endpoint_parameters_endpoint ON coc_endpoint_parameters(endpoint_id);
        CREATE INDEX IF NOT EXISTS idx_endpoint_parameters_name ON coc_endpoint_parameters(name);
        CREATE INDEX IF NOT EXISTS idx_endpoint_parameters_required ON coc_endpoint_parameters(required);
        """
        
        # Endpoint Responses table
        endpoint_responses_sql = """
        CREATE TABLE IF NOT EXISTS coc_endpoint_responses (
            id SERIAL PRIMARY KEY,
            endpoint_id INTEGER NOT NULL,
            status_code INTEGER NOT NULL,
            description TEXT,
            schema_definition JSONB,
            examples JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (endpoint_id) REFERENCES coc_endpoint_schemas(id) ON DELETE CASCADE
        );
        
        CREATE INDEX IF NOT EXISTS idx_endpoint_responses_endpoint ON coc_endpoint_responses(endpoint_id);
        CREATE INDEX IF NOT EXISTS idx_endpoint_responses_status ON coc_endpoint_responses(status_code);
        """
        
        # Schema Cache table
        schema_cache_sql = """
        CREATE TABLE IF NOT EXISTS coc_schema_cache (
            id SERIAL PRIMARY KEY,
            cache_key VARCHAR(255) NOT NULL UNIQUE,
            data JSONB NOT NULL,
            hit_count INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL,
            last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_schema_cache_key ON coc_schema_cache(cache_key);
        CREATE INDEX IF NOT EXISTS idx_schema_cache_expires ON coc_schema_cache(expires_at);
        """
        
        # Discovery Statistics table
        discovery_stats_sql = """
        CREATE TABLE IF NOT EXISTS coc_discovery_statistics (
            id SERIAL PRIMARY KEY,
            total_services INTEGER DEFAULT 0,
            discovered_endpoints INTEGER DEFAULT 0,
            completeness_score DECIMAL(5,2) DEFAULT 0.0,
            cache_hit_rate DECIMAL(5,2) DEFAULT 0.0,
            discovery_errors JSONB DEFAULT '[]',
            last_full_discovery TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        # Tool Definitions table
        tool_definitions_sql = """
        CREATE TABLE IF NOT EXISTS coc_tool_definitions (
            id SERIAL PRIMARY KEY,
            name VARCHAR(200) NOT NULL UNIQUE,
            service_name VARCHAR(100) NOT NULL,
            endpoint_path VARCHAR(500) NOT NULL,
            method VARCHAR(10) NOT NULL,
            description TEXT,
            parameters JSONB DEFAULT '[]',
            responses JSONB DEFAULT '[]',
            complexity VARCHAR(20) DEFAULT 'moderate',
            estimated_response_time INTEGER DEFAULT 1000,
            risk_level VARCHAR(20) DEFAULT 'low',
            tags JSONB DEFAULT '[]',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (service_name) REFERENCES coc_service_schemas(name) ON DELETE CASCADE
        );
        
        CREATE INDEX IF NOT EXISTS idx_tool_definitions_service ON coc_tool_definitions(service_name);
        CREATE INDEX IF NOT EXISTS idx_tool_definitions_complexity ON coc_tool_definitions(complexity);
        CREATE INDEX IF NOT EXISTS idx_tool_definitions_risk ON coc_tool_definitions(risk_level);
        """
        
        # Capability Mappings table
        capability_mappings_sql = """
        CREATE TABLE IF NOT EXISTS coc_capability_mappings (
            id SERIAL PRIMARY KEY,
            intent VARCHAR(100) NOT NULL,
            capabilities JSONB NOT NULL DEFAULT '[]',
            confidence DECIMAL(5,4) DEFAULT 0.0,
            reasoning TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_capability_mappings_intent ON coc_capability_mappings(intent);
        CREATE INDEX IF NOT EXISTS idx_capability_mappings_confidence ON coc_capability_mappings(confidence);
        """
        
        async with self.pool.acquire() as conn:
            # Execute all table creation statements
            await conn.execute(service_schemas_sql)
            await conn.execute(service_availability_sql)
            await conn.execute(endpoint_schemas_sql)
            await conn.execute(endpoint_parameters_sql)
            await conn.execute(endpoint_responses_sql)
            await conn.execute(schema_cache_sql)
            await conn.execute(discovery_stats_sql)
            await conn.execute(tool_definitions_sql)
            await conn.execute(capability_mappings_sql)
            
            self.logger.info("All Platform Schema Generator tables created successfully")
    
    # ==================== SERVICE SCHEMA OPERATIONS ====================

    async def save_service_schema(self, service_schema) -> None:
        """Save or update a service schema"""
        async with self.pool.acquire() as conn:
            # Insert or update service schema
            await conn.execute("""
                INSERT INTO coc_service_schemas
                (name, version, base_url, discovery_method, completeness_score, openapi_spec, last_discovered)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                ON CONFLICT (name) DO UPDATE SET
                    version = EXCLUDED.version,
                    base_url = EXCLUDED.base_url,
                    discovery_method = EXCLUDED.discovery_method,
                    completeness_score = EXCLUDED.completeness_score,
                    openapi_spec = EXCLUDED.openapi_spec,
                    last_discovered = EXCLUDED.last_discovered,
                    updated_at = CURRENT_TIMESTAMP
            """,
                service_schema.name,
                service_schema.version,
                service_schema.base_url,
                service_schema.discovery_method,
                service_schema.completeness_score,
                json.dumps(asdict(service_schema.openapi_spec)) if service_schema.openapi_spec else None,
                service_schema.last_discovered
            )

            # Save service availability
            await conn.execute("""
                INSERT INTO coc_service_availability
                (service_name, status, uptime, response_time, error_rate, health_score, last_check)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                ON CONFLICT (service_name) DO UPDATE SET
                    status = EXCLUDED.status,
                    uptime = EXCLUDED.uptime,
                    response_time = EXCLUDED.response_time,
                    error_rate = EXCLUDED.error_rate,
                    health_score = EXCLUDED.health_score,
                    last_check = EXCLUDED.last_check
            """,
                service_schema.name,
                service_schema.availability.status,
                service_schema.availability.uptime,
                service_schema.availability.response_time,
                service_schema.availability.error_rate,
                service_schema.availability.health_score,
                service_schema.availability.last_check
            )

            # Save endpoints
            for endpoint in service_schema.endpoints:
                await self._save_endpoint_schema(conn, service_schema.name, endpoint)

    async def _save_endpoint_schema(self, conn, service_name: str, endpoint) -> None:
        """Save endpoint schema and related data"""
        # Insert or update endpoint
        endpoint_id = await conn.fetchval("""
            INSERT INTO coc_endpoint_schemas
            (service_name, path, method, description, tags, complexity,
             estimated_response_time, risk_level, deprecated, security_requirements)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            ON CONFLICT (service_name, path, method) DO UPDATE SET
                description = EXCLUDED.description,
                tags = EXCLUDED.tags,
                complexity = EXCLUDED.complexity,
                estimated_response_time = EXCLUDED.estimated_response_time,
                risk_level = EXCLUDED.risk_level,
                deprecated = EXCLUDED.deprecated,
                security_requirements = EXCLUDED.security_requirements,
                updated_at = CURRENT_TIMESTAMP
            RETURNING id
        """,
            service_name, endpoint.path, endpoint.method, endpoint.description,
            json.dumps(endpoint.tags), endpoint.complexity,
            endpoint.estimated_response_time, endpoint.risk_level,
            endpoint.deprecated, json.dumps(endpoint.security or [])
        )

        # Clear existing parameters and responses
        await conn.execute("DELETE FROM coc_endpoint_parameters WHERE endpoint_id = $1", endpoint_id)
        await conn.execute("DELETE FROM coc_endpoint_responses WHERE endpoint_id = $1", endpoint_id)

        # Save parameters
        for param in endpoint.parameters:
            await conn.execute("""
                INSERT INTO coc_endpoint_parameters
                (endpoint_id, name, type, required, description, schema_definition, example_value, enum_values)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """,
                endpoint_id, param.name, param.type, param.required,
                param.description, json.dumps(param.schema),
                json.dumps(param.example) if param.example else None,
                json.dumps(param.enum) if param.enum else None
            )

        # Save responses
        for response in endpoint.responses:
            await conn.execute("""
                INSERT INTO coc_endpoint_responses
                (endpoint_id, status_code, description, schema_definition, examples)
                VALUES ($1, $2, $3, $4, $5)
            """,
                endpoint_id, response.status_code, response.description,
                json.dumps(response.schema), json.dumps(response.examples) if response.examples else None
            )

    # ==================== CACHE OPERATIONS ====================

    async def get_from_cache(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow("""
                SELECT data, expires_at FROM coc_schema_cache
                WHERE cache_key = $1 AND expires_at > CURRENT_TIMESTAMP
            """, key)

            if row:
                # Update hit count and last accessed
                await conn.execute("""
                    UPDATE coc_schema_cache
                    SET hit_count = hit_count + 1, last_accessed = CURRENT_TIMESTAMP
                    WHERE cache_key = $1
                """, key)

                return json.loads(row['data'])

            return None

    async def store_in_cache(self, key: str, data: Any, ttl_hours: int = 1) -> None:
        """Store item in cache"""
        expires_at = datetime.now() + timedelta(hours=ttl_hours)

        async with self.pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO coc_schema_cache (cache_key, data, expires_at)
                VALUES ($1, $2, $3)
                ON CONFLICT (cache_key) DO UPDATE SET
                    data = EXCLUDED.data,
                    expires_at = EXCLUDED.expires_at,
                    last_accessed = CURRENT_TIMESTAMP
            """, key, json.dumps(data), expires_at)

    async def cleanup_expired_cache(self) -> int:
        """Remove expired cache entries"""
        async with self.pool.acquire() as conn:
            result = await conn.execute("""
                DELETE FROM coc_schema_cache WHERE expires_at <= CURRENT_TIMESTAMP
            """)
            return int(result.split()[-1])  # Extract count from "DELETE n"

    # ==================== STATISTICS OPERATIONS ====================

    async def save_discovery_statistics(self, stats: Dict[str, Any]) -> None:
        """Save discovery statistics"""
        async with self.pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO coc_discovery_statistics
                (total_services, discovered_endpoints, completeness_score,
                 cache_hit_rate, discovery_errors, last_full_discovery)
                VALUES ($1, $2, $3, $4, $5, $6)
            """,
                stats.get('total_services', 0),
                stats.get('discovered_endpoints', 0),
                stats.get('completeness_score', 0.0),
                stats.get('cache_hit_rate', 0.0),
                json.dumps(stats.get('discovery_errors', [])),
                stats.get('last_full_discovery')
            )

    async def get_latest_statistics(self) -> Optional[Dict[str, Any]]:
        """Get latest discovery statistics"""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow("""
                SELECT * FROM coc_discovery_statistics
                ORDER BY created_at DESC LIMIT 1
            """)

            if row:
                return dict(row)
            return None

    # ==================== TOOL DEFINITION OPERATIONS ====================

    async def save_tool_definition(self, tool_def: Dict[str, Any]) -> None:
        """Save tool definition"""
        async with self.pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO coc_tool_definitions
                (name, service_name, endpoint_path, method, description, parameters,
                 responses, complexity, estimated_response_time, risk_level, tags)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                ON CONFLICT (name) DO UPDATE SET
                    description = EXCLUDED.description,
                    parameters = EXCLUDED.parameters,
                    responses = EXCLUDED.responses,
                    complexity = EXCLUDED.complexity,
                    estimated_response_time = EXCLUDED.estimated_response_time,
                    risk_level = EXCLUDED.risk_level,
                    tags = EXCLUDED.tags,
                    updated_at = CURRENT_TIMESTAMP
            """,
                tool_def['name'], tool_def.get('service_name', ''),
                tool_def.get('url', ''), tool_def.get('method', 'GET'),
                tool_def.get('description', ''), json.dumps(tool_def.get('parameters', [])),
                json.dumps(tool_def.get('responses', [])), tool_def.get('complexity', 'moderate'),
                tool_def.get('estimated_response_time', 1000), tool_def.get('risk_level', 'low'),
                json.dumps(tool_def.get('tags', []))
            )

    async def close(self) -> None:
        """Close database connection pool"""
        if self.pool:
            await self.pool.close()
            self.logger.info("Database connection pool closed")
