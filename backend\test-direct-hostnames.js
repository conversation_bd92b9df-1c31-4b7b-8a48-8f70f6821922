/**
 * ScaleGrid PostgreSQL Connection Test
 * Testing connection to your specific PostgreSQL server
 */

require('dotenv').config({ path: __dirname + '/.env' });
const { Pool } = require('pg');
const Redis = require('ioredis');

async function testPostgreSQLDirectHostname() {
  console.log('🐘 Testing PostgreSQL with Direct Hostname');
  console.log('==========================================');
  console.log('Host: SG-rough-guava-317-7201-pgsql-master.servers.mongodirector.com');
  console.log('Port: 6432');
  console.log('Database: postgres');
  console.log('User: sgpostgres');
  console.log('');

  try {
    const pool = new Pool({
      host: 'SG-rough-guava-317-7201-pgsql-master.servers.mongodirector.com',
      port: 6432,
      database: 'postgres',
      user: 'sgpostgres',
      password: 'd3tnRHMiuiNI+OTc',
      ssl: false,
      connectionTimeoutMillis: 30000,
      max: 1
    });

    console.log('🔄 Connecting to PostgreSQL...');
    const startTime = Date.now();
    
    const client = await Promise.race([
      pool.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout after 30s')), 30000)
      )
    ]);

    const connectTime = Date.now() - startTime;
    console.log(`✅ Connected successfully (${connectTime}ms)`);

    // Test query
    const result = await client.query('SELECT version() as version, current_database() as database');
    console.log(`📊 Version: ${result.rows[0].version}`);
    console.log(`📊 Database: ${result.rows[0].database}`);

    client.release();
    await pool.end();

    const totalTime = Date.now() - startTime;
    console.log(`🎉 PostgreSQL test completed successfully (${totalTime}ms total)`);
    return true;

  } catch (error) {
    console.log(`❌ PostgreSQL connection failed: ${error.message}`);
    console.log(`   Error Type: ${error.constructor.name}`);
    if (error.code) console.log(`   Error Code: ${error.code}`);
    return false;
  }
}

async function testRedisDirectHostname() {
  console.log('\n🔴 Testing Redis with Direct Hostname');
  console.log('=====================================');
  console.log('Host: SG-quirky-manner-4023-75401.servers.mongodirector.com');
  console.log('Port: 6379');
  console.log('');

  try {
    const redis = new Redis({
      host: 'SG-quirky-manner-4023-75401.servers.mongodirector.com',
      port: 6379,
      password: 'zyPGirgn325ijWMmknFyXkyHPV76nsWv',
      connectTimeout: 30000,
      commandTimeout: 15000,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });

    console.log('🔄 Connecting to Redis...');
    const startTime = Date.now();

    await Promise.race([
      redis.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout after 30s')), 30000)
      )
    ]);

    const connectTime = Date.now() - startTime;
    console.log(`✅ Connected successfully (${connectTime}ms)`);

    // Test commands
    const pong = await redis.ping();
    console.log(`📊 Ping response: ${pong}`);

    const info = await redis.info('server');
    const serverInfo = info.split('\r\n').find(line => line.startsWith('redis_version:'));
    if (serverInfo) {
      console.log(`📊 Redis version: ${serverInfo.split(':')[1]}`);
    }

    await redis.disconnect();

    const totalTime = Date.now() - startTime;
    console.log(`🎉 Redis test completed successfully (${totalTime}ms total)`);
    return true;

  } catch (error) {
    console.log(`❌ Redis connection failed: ${error.message}`);
    console.log(`   Error Type: ${error.constructor.name}`);
    if (error.code) console.log(`   Error Code: ${error.code}`);
    return false;
  }
}

async function main() {
  console.log('🚀 ScaleGrid Direct Hostname Connection Test');
  console.log('============================================');
  console.log('Using exact hostnames provided by ScaleGrid');
  console.log('No IP resolution - direct hostname connection');
  console.log('');

  const results = [];

  // Test PostgreSQL
  results.push(await testPostgreSQLDirectHostname());

  // Test Redis
  results.push(await testRedisDirectHostname());

  // Summary
  console.log('\n📊 Test Summary');
  console.log('===============');
  const successful = results.filter(r => r).length;
  const total = results.length;

  console.log(`Total tests: ${total}`);
  console.log(`Successful: ${successful}`);
  console.log(`Failed: ${total - successful}`);

  if (successful === total) {
    console.log('\n🎉 All connections successful using direct hostnames!');
    console.log('✅ ScaleGrid services are accessible and ready to use.');
    process.exit(0);
  } else if (successful > 0) {
    console.log('\n⚠️ Partial success - some services are accessible.');
    process.exit(1);
  } else {
    console.log('\n❌ All connections failed using direct hostnames.');
    console.log('💡 Check ScaleGrid instance status and IP whitelist configuration.');
    process.exit(1);
  }
}

main().catch(error => {
  console.error('💥 Test suite crashed:', error);
  process.exit(1);
});
