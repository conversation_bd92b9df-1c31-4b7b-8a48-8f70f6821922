#!/usr/bin/env python3
"""
Setup Database Integration for Enhanced Platform Schema Generator

This script sets up the complete database integration including:
1. Registering LLM service to universal database
2. Enabling Enhanced Platform Schema Generator PostgreSQL integration
3. Testing the database connection and functionality
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add the parent directory to the path to import our modules
sys.path.append(str(Path(__file__).parent.parent))

from scripts.register_llm_service_to_database import UniversalServiceRegistry
from services.coc.database.models import PlatformSchemaDB

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def setup_database_integration():
    """Complete database integration setup"""
    
    logger.info("🚀 Setting up Enhanced Platform Schema Generator Database Integration")
    logger.info("=" * 80)
    
    # Database connection details
    db_host = 'SG-heady-war-6640-7193-pgsql-master.servers.mongodirector.com'
    db_port = '5432'
    db_name = 'script_ai_platform'
    db_user = 'postgres'
    
    # For security, we'll prompt for password or use environment variable
    db_password = os.getenv('POSTGRES_PASSWORD')
    if not db_password:
        logger.info("📝 PostgreSQL password not found in environment")
        logger.info("Please set POSTGRES_PASSWORD environment variable or enter it now:")
        try:
            import getpass
            db_password = getpass.getpass("PostgreSQL Password: ")
        except KeyboardInterrupt:
            logger.info("\n❌ Setup cancelled by user")
            return False
        except Exception:
            logger.error("❌ Failed to get password input")
            return False
    
    if not db_password:
        logger.error("❌ PostgreSQL password is required")
        return False
    
    connection_string = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    logger.info(f"🔗 Connecting to: {db_host}:{db_port}/{db_name}")
    
    # Step 1: Test database connection
    logger.info("\n📊 Step 1: Testing Database Connection")
    logger.info("-" * 50)
    
    try:
        import asyncpg
        conn = await asyncpg.connect(connection_string)
        result = await conn.fetchval("SELECT version()")
        await conn.close()
        logger.info(f"✅ Database connection successful")
        logger.info(f"   PostgreSQL Version: {result.split(',')[0]}")
    except Exception as error:
        logger.error(f"❌ Database connection failed: {error}")
        logger.error("   Please check:")
        logger.error("   - Database host is accessible")
        logger.error("   - Credentials are correct")
        logger.error("   - Network connectivity")
        return False
    
    # Step 2: Register LLM service to universal database
    logger.info("\n📊 Step 2: Registering LLM Service to Universal Database")
    logger.info("-" * 50)
    
    registry = UniversalServiceRegistry(connection_string)
    
    try:
        await registry.initialize()
        await registry.register_llm_service()
        logger.info("✅ LLM service registered successfully")
    except Exception as error:
        logger.error(f"❌ LLM service registration failed: {error}")
        return False
    finally:
        await registry.close()
    
    # Step 3: Initialize Enhanced Platform Schema Generator tables
    logger.info("\n📊 Step 3: Initializing Enhanced Platform Schema Generator Tables")
    logger.info("-" * 50)
    
    platform_db = PlatformSchemaDB(connection_string)
    
    try:
        await platform_db.initialize()
        logger.info("✅ Enhanced Platform Schema Generator tables created successfully")
    except Exception as error:
        logger.error(f"❌ Platform Schema Generator table creation failed: {error}")
        return False
    finally:
        await platform_db.close()
    
    # Step 4: Verify integration
    logger.info("\n📊 Step 4: Verifying Database Integration")
    logger.info("-" * 50)
    
    try:
        conn = await asyncpg.connect(connection_string)
        
        # Check universal service registry
        service_count = await conn.fetchval("SELECT COUNT(*) FROM platform_services WHERE service_name = 'llm-service'")
        endpoint_count = await conn.fetchval("SELECT COUNT(*) FROM platform_service_endpoints WHERE service_name = 'llm-service'")
        
        # Check Enhanced Platform Schema Generator tables
        coc_tables = await conn.fetch("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name LIKE 'coc_%'
        """)
        
        await conn.close()
        
        logger.info(f"✅ LLM Service Registration: {service_count} service, {endpoint_count} endpoints")
        logger.info(f"✅ COC Tables Created: {len(coc_tables)} tables")
        
        if service_count > 0 and len(coc_tables) > 0:
            logger.info("✅ Database integration verification successful")
        else:
            logger.error("❌ Database integration verification failed")
            return False
            
    except Exception as error:
        logger.error(f"❌ Database integration verification failed: {error}")
        return False
    
    # Step 5: Update environment configuration
    logger.info("\n📊 Step 5: Updating Environment Configuration")
    logger.info("-" * 50)
    
    env_vars = {
        'POSTGRES_HOST': db_host,
        'POSTGRES_PORT': db_port,
        'POSTGRES_DB': db_name,
        'POSTGRES_USER': db_user,
        'POSTGRES_PASSWORD': db_password
    }
    
    # Set environment variables for current session
    for key, value in env_vars.items():
        os.environ[key] = value
    
    logger.info("✅ Environment variables configured")
    logger.info("   Note: For persistent configuration, add these to your .env file:")
    for key, value in env_vars.items():
        if key != 'POSTGRES_PASSWORD':
            logger.info(f"   {key}={value}")
        else:
            logger.info(f"   {key}=<your_password>")
    
    # Success summary
    logger.info("\n" + "=" * 80)
    logger.info("🎉 Enhanced Platform Schema Generator Database Integration Complete!")
    logger.info("=" * 80)
    
    logger.info("\n✅ What was accomplished:")
    logger.info("   - Database connection verified")
    logger.info("   - Universal service registry tables created")
    logger.info("   - LLM service registered with full configuration")
    logger.info("   - Enhanced Platform Schema Generator tables created")
    logger.info("   - Database integration enabled in COC configuration")
    logger.info("   - Environment variables configured")
    
    logger.info("\n🚀 Next steps:")
    logger.info("   1. Restart the LLM service to enable PostgreSQL integration")
    logger.info("   2. The Enhanced Platform Schema Generator will now use PostgreSQL")
    logger.info("   3. All service discovery data will be persisted to database")
    logger.info("   4. Cache and statistics will be stored in PostgreSQL")
    
    logger.info(f"\n📊 Database Details:")
    logger.info(f"   Host: {db_host}")
    logger.info(f"   Port: {db_port}")
    logger.info(f"   Database: {db_name}")
    logger.info(f"   User: {db_user}")
    
    return True

async def main():
    """Main setup function"""
    try:
        success = await setup_database_integration()
        if success:
            logger.info("\n🎯 Database integration setup completed successfully!")
            logger.info("You can now restart the LLM service to use PostgreSQL.")
        else:
            logger.error("\n❌ Database integration setup failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("\n❌ Setup cancelled by user")
        sys.exit(1)
    except Exception as error:
        logger.error(f"\n❌ Unexpected error during setup: {error}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
