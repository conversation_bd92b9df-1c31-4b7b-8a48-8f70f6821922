/**
 * Test Python Installation and Twikit Setup
 * Node.js script to verify Python environment and install Twikit
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const VENV_DIR = 'python_env';
const REQUIREMENTS_FILE = 'requirements-python.txt';

console.log('🐍 Testing Python Installation and Twikit Setup');
console.log('================================================');

// Function to run command and return promise
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔄 Running: ${command} ${args.join(' ')}`);
    
    const process = spawn(command, args, {
      stdio: 'pipe',
      shell: true,
      ...options
    });

    let stdout = '';
    let stderr = '';

    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    process.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject({ stdout, stderr, code });
      }
    });

    process.on('error', (error) => {
      reject({ error: error.message, code: -1 });
    });
  });
}

// Function to check Python installation
async function checkPython() {
  console.log('\n📋 Checking Python installation...');
  
  try {
    const result = await runCommand('python', ['--version']);
    console.log(`✅ Python found: ${result.stdout.trim()}`);
    
    // Check if version is 3.8+
    const versionMatch = result.stdout.match(/Python (\d+)\.(\d+)/);
    if (versionMatch) {
      const major = parseInt(versionMatch[1]);
      const minor = parseInt(versionMatch[2]);
      
      if (major >= 3 && minor >= 8) {
        console.log('✅ Python version is sufficient (3.8+)');
        return true;
      } else {
        console.log(`❌ Python 3.8+ required, found ${major}.${minor}`);
        return false;
      }
    }
  } catch (error) {
    console.log('❌ Python not found or not accessible');
    console.log('Please install Python 3.8+ from https://python.org');
    return false;
  }
}

// Function to check pip
async function checkPip() {
  console.log('\n📦 Checking pip installation...');
  
  try {
    const result = await runCommand('pip', ['--version']);
    console.log(`✅ pip found: ${result.stdout.trim()}`);
    return true;
  } catch (error) {
    console.log('❌ pip not found');
    return false;
  }
}

// Function to check if virtual environment exists and is valid
function checkVirtualEnv() {
  const isWindows = process.platform === 'win32';
  const pythonExe = isWindows
    ? path.join(VENV_DIR, 'Scripts', 'python.exe')
    : path.join(VENV_DIR, 'bin', 'python');

  return fs.existsSync(VENV_DIR) && fs.existsSync(pythonExe);
}

// Function to create virtual environment (only if needed)
async function createVirtualEnv(force = false) {
  console.log('\n🏗️ Checking Python virtual environment...');

  // Check if virtual environment already exists and is valid
  if (!force && checkVirtualEnv()) {
    console.log('✅ Virtual environment already exists and is valid');
    return true;
  }

  // Remove existing environment if it exists and we're forcing recreation
  if (force && fs.existsSync(VENV_DIR)) {
    console.log('⚠️ Removing existing virtual environment (forced)...');
    fs.rmSync(VENV_DIR, { recursive: true, force: true });
  }

  console.log('🔨 Creating new virtual environment...');
  try {
    await runCommand('python', ['-m', 'venv', VENV_DIR]);
    console.log(`✅ Virtual environment created: ${VENV_DIR}`);
    return true;
  } catch (error) {
    console.log('❌ Failed to create virtual environment');
    console.log(error.stderr || error.error);
    return false;
  }
}

// Function to create requirements file
function createRequirementsFile() {
  console.log('\n📝 Creating requirements file...');
  
  const requirements = `# Twikit and its dependencies
twikit>=2.3.0

# Core dependencies
httpx[socks]>=0.24.0
filetype>=1.2.0
beautifulsoup4>=4.11.0
pyotp>=2.8.0
lxml>=4.9.0
webvtt-py>=0.4.6
m3u8>=3.5.0
Js2Py>=0.74

# Additional utilities for Node.js integration
python-dotenv>=1.0.0
aiofiles>=23.0.0
pydantic>=2.0.0
`;

  fs.writeFileSync(REQUIREMENTS_FILE, requirements);
  console.log(`✅ Requirements file created: ${REQUIREMENTS_FILE}`);
}

// Function to check if dependencies are already installed
async function checkDependenciesInstalled(pipExe) {
  try {
    // Check if twikit is installed (main dependency)
    const result = await runCommand(pipExe, ['show', 'twikit']);
    return result.stdout.includes('Name: twikit');
  } catch (error) {
    return false;
  }
}

// Function to install dependencies (only if needed)
async function installDependencies(force = false) {
  console.log('\n📦 Checking Python dependencies...');

  // Determine the correct python executable path
  const isWindows = process.platform === 'win32';
  const pythonExe = isWindows
    ? path.join(VENV_DIR, 'Scripts', 'python.exe')
    : path.join(VENV_DIR, 'bin', 'python');

  const pipExe = isWindows
    ? path.join(VENV_DIR, 'Scripts', 'pip.exe')
    : path.join(VENV_DIR, 'bin', 'pip');

  // Check if dependencies are already installed
  if (!force) {
    const dependenciesInstalled = await checkDependenciesInstalled(pipExe);
    if (dependenciesInstalled) {
      console.log('✅ Dependencies already installed, skipping installation');
      return { pythonExe, pipExe };
    }
  }

  try {
    console.log('🔨 Installing/updating Python dependencies...');

    // Upgrade pip first (only if needed)
    console.log('⬆️ Ensuring pip is up to date...');
    await runCommand(pythonExe, ['-m', 'pip', 'install', '--upgrade', 'pip', '--quiet']);

    // Install requirements with optimizations
    console.log('📦 Installing requirements (this may take a few minutes on first run)...');
    await runCommand(pipExe, [
      'install',
      '-r', REQUIREMENTS_FILE,
      '--upgrade-strategy', 'only-if-needed',  // Only upgrade if needed
      '--cache-dir', path.join(VENV_DIR, 'pip-cache'),  // Use local cache
      '--quiet'  // Reduce output noise
    ]);

    console.log('✅ Dependencies installed successfully');
    return { pythonExe, pipExe };
  } catch (error) {
    console.log('❌ Failed to install dependencies');
    console.log(error.stderr || error.error);
    return null;
  }
}

// Function to verify Twikit installation
async function verifyTwikit(pythonExe) {
  console.log('\n🔍 Verifying Twikit installation...');

  // Create a temporary Python script for verification
  const testScriptContent = `import sys
import twikit
from twikit import Client

print(f"[OK] Twikit {twikit.__version__} installed successfully")
print(f"[OK] Python {sys.version.split()[0]}")
print("[OK] Twikit Client import successful")
`;

  const testScriptPath = 'verify_twikit.py';
  fs.writeFileSync(testScriptPath, testScriptContent);

  try {
    const result = await runCommand(pythonExe, [testScriptPath]);
    console.log(result.stdout);

    // Clean up
    fs.unlinkSync(testScriptPath);
    return true;
  } catch (error) {
    console.log('❌ Twikit verification failed');
    console.log(error.stderr || error.stdout);

    // Clean up
    if (fs.existsSync(testScriptPath)) {
      fs.unlinkSync(testScriptPath);
    }
    return false;
  }
}

// Main function
async function main() {
  try {
    // Check for command line arguments
    const args = process.argv.slice(2);
    const forceRebuild = args.includes('--force') || args.includes('-f');
    const skipVerify = args.includes('--skip-verify');

    if (forceRebuild) {
      console.log('🔄 Force rebuild requested - will recreate virtual environment');
    }

    // Check Python
    const pythonOk = await checkPython();
    if (!pythonOk) {
      process.exit(1);
    }

    // Check pip
    const pipOk = await checkPip();
    if (!pipOk) {
      process.exit(1);
    }

    // Create virtual environment (only if needed, unless forced)
    const venvOk = await createVirtualEnv(forceRebuild);
    if (!venvOk) {
      process.exit(1);
    }

    // Create requirements file (always update to latest)
    createRequirementsFile();

    // Install dependencies (only if needed, unless forced)
    const installResult = await installDependencies(forceRebuild);
    if (!installResult) {
      process.exit(1);
    }

    // Verify installation (unless skipped)
    if (!skipVerify) {
      const verifyOk = await verifyTwikit(installResult.pythonExe);
      if (!verifyOk) {
        console.log('⚠️ Verification failed, but continuing...');
      }
    } else {
      console.log('⏭️ Skipping verification as requested');
    }

    console.log('\n🎉 Twikit setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`  ✅ Virtual environment: ${VENV_DIR}`);
    console.log(`  ✅ Requirements file: ${REQUIREMENTS_FILE}`);
    console.log(`  ✅ Python executable: ${installResult.pythonExe}`);
    console.log(`  ✅ Pip executable: ${installResult.pipExe}`);
    
    console.log('\n🚀 Next steps:');
    console.log('  1. Integrate with Node.js backend');
    console.log('  2. Create Twikit wrapper service');
    console.log('  3. Configure Docker support');

    console.log('\n💡 Usage tips:');
    console.log('  • Use --force to rebuild everything from scratch');
    console.log('  • Use --skip-verify to skip Twikit verification');
    console.log('  • Dependencies are cached to speed up subsequent builds');

    console.log('\n✨ Ready for X/Twitter automation!');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  runCommand,
  checkPython,
  checkPip,
  createVirtualEnv,
  installDependencies,
  verifyTwikit
};
