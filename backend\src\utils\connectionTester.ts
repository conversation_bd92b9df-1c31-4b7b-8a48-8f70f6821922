/**
 * Connection Tester Utility
 * 
 * Tests ScaleGrid PostgreSQL and Redis connections with detailed diagnostics
 */

import { Pool } from 'pg';
import Redis from 'ioredis';
import { logger } from './logger';
import dns from 'dns';
import { promisify } from 'util';

const dnsLookup = promisify(dns.lookup);

interface ConnectionTestResult {
  service: string;
  endpoint: string;
  success: boolean;
  latency?: number;
  error?: string;
  details?: any;
}

export class ConnectionTester {
  /**
   * Test ScaleGrid PostgreSQL connection
   */
  static async testPostgreSQL(): Promise<ConnectionTestResult> {
    const endpoint = `${process.env.SCALEGRID_PG_HOST}:${process.env.SCALEGRID_PG_PORT}`;
    const startTime = Date.now();

    try {
      logger.info(`🔍 Testing PostgreSQL connection to ${endpoint}...`);

      // Step 1: DNS Resolution Test
      const dnsResult = await this.testDNSResolution(process.env.SCALEGRID_PG_HOST!);
      if (!dnsResult.success) {
        return {
          service: 'PostgreSQL',
          endpoint,
          success: false,
          error: `DNS resolution failed: ${dnsResult.error}`,
          details: dnsResult
        };
      }

      // Step 2: Connection Test
      const pool = new Pool({
        host: process.env.SCALEGRID_PG_HOST,
        port: parseInt(process.env.SCALEGRID_PG_PORT || '6432'),
        database: process.env.SCALEGRID_PG_DATABASE || 'defaultdb',
        user: process.env.SCALEGRID_PG_USER,
        password: process.env.SCALEGRID_PG_PASSWORD,
        ssl: {
          rejectUnauthorized: false
        },
        connectionTimeoutMillis: 30000,
        query_timeout: 15000,
        max: 1
      });

      const client = await Promise.race([
        pool.connect(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Connection timeout after 30s')), 30000)
        )
      ]) as any;

      // Step 3: Query Test
      await client.query('SELECT 1 as test');
      client.release();
      await pool.end();

      const latency = Date.now() - startTime;
      logger.info(`✅ PostgreSQL connection successful (${latency}ms)`);

      return {
        service: 'PostgreSQL',
        endpoint,
        success: true,
        latency,
        details: {
          dnsResolution: dnsResult,
          database: process.env.SCALEGRID_PG_DATABASE,
          ssl: true
        }
      };

    } catch (error) {
      const latency = Date.now() - startTime;
      logger.error(`❌ PostgreSQL connection failed (${latency}ms):`, error);

      return {
        service: 'PostgreSQL',
        endpoint,
        success: false,
        latency,
        error: error instanceof Error ? error.message : String(error),
        details: {
          errorType: error instanceof Error ? error.constructor.name : 'Unknown',
          stack: error instanceof Error ? error.stack : undefined
        }
      };
    }
  }

  /**
   * Test ScaleGrid Redis connection
   */
  static async testRedis(): Promise<ConnectionTestResult> {
    const endpoint = `${process.env.SCALEGRID_REDIS_HOST}:${process.env.SCALEGRID_REDIS_PORT}`;
    const startTime = Date.now();

    try {
      logger.info(`🔍 Testing Redis connection to ${endpoint}...`);

      // Step 1: DNS Resolution Test
      const dnsResult = await this.testDNSResolution(process.env.SCALEGRID_REDIS_HOST!);
      if (!dnsResult.success) {
        return {
          service: 'Redis',
          endpoint,
          success: false,
          error: `DNS resolution failed: ${dnsResult.error}`,
          details: dnsResult
        };
      }

      // Step 2: Connection Test
      const redis = new Redis({
        host: process.env.SCALEGRID_REDIS_HOST,
        port: parseInt(process.env.SCALEGRID_REDIS_PORT || '6379'),
        password: process.env.SCALEGRID_REDIS_PASSWORD,
        connectTimeout: 30000,
        commandTimeout: 15000,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true
      });

      await Promise.race([
        redis.connect(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Connection timeout after 30s')), 30000)
        )
      ]);

      // Step 3: Command Test
      await redis.ping();
      await redis.disconnect();

      const latency = Date.now() - startTime;
      logger.info(`✅ Redis connection successful (${latency}ms)`);

      return {
        service: 'Redis',
        endpoint,
        success: true,
        latency,
        details: {
          dnsResolution: dnsResult,
          auth: !!process.env.SCALEGRID_REDIS_PASSWORD
        }
      };

    } catch (error) {
      const latency = Date.now() - startTime;
      logger.error(`❌ Redis connection failed (${latency}ms):`, error);

      return {
        service: 'Redis',
        endpoint,
        success: false,
        latency,
        error: error instanceof Error ? error.message : String(error),
        details: {
          errorType: error instanceof Error ? error.constructor.name : 'Unknown',
          stack: error instanceof Error ? error.stack : undefined
        }
      };
    }
  }

  /**
   * Test DNS resolution
   */
  private static async testDNSResolution(hostname: string): Promise<ConnectionTestResult> {
    const startTime = Date.now();

    try {
      const result = await Promise.race([
        dnsLookup(hostname, { all: true }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('DNS timeout after 10s')), 10000)
        )
      ]) as dns.LookupAddress[];

      const latency = Date.now() - startTime;
      const addresses = result.map(addr => addr.address);

      return {
        service: 'DNS',
        endpoint: hostname,
        success: true,
        latency,
        details: {
          addresses,
          count: addresses.length
        }
      };

    } catch (error) {
      const latency = Date.now() - startTime;
      return {
        service: 'DNS',
        endpoint: hostname,
        success: false,
        latency,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Run comprehensive connection tests
   */
  static async runAllTests(): Promise<ConnectionTestResult[]> {
    logger.info('🚀 Starting comprehensive ScaleGrid connection tests...');

    const results: ConnectionTestResult[] = [];

    // Test PostgreSQL
    results.push(await this.testPostgreSQL());

    // Test Redis
    results.push(await this.testRedis());

    // Log summary
    const successful = results.filter(r => r.success).length;
    const total = results.length;

    logger.info(`📊 Connection test summary: ${successful}/${total} successful`);
    
    results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const latency = result.latency ? `(${result.latency}ms)` : '';
      logger.info(`${status} ${result.service}: ${result.endpoint} ${latency}`);
      
      if (!result.success && result.error) {
        logger.error(`   Error: ${result.error}`);
      }
    });

    return results;
  }
}
