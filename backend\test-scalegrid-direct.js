/**
 * Direct ScaleGrid Connection Test
 * Based on ScaleGrid documentation and best practices
 */

require('dotenv').config();
const { Pool } = require('pg');

async function testScaleGridConnection() {
  console.log('🚀 ScaleGrid Direct Connection Test');
  console.log('===================================');
  
  // Method 1: Using connection string with sslmode=disable
  console.log('\n📋 Method 1: Connection String with sslmode=disable');
  const connectionString = "postgresql://sgpostgres:<EMAIL>:6432/postgres?sslmode=disable";
  
  try {
    const pool1 = new Pool({
      connectionString: connectionString,
      connectionTimeoutMillis: 30000,
      idleTimeoutMillis: 30000,
      max: 1
    });

    console.log('🔄 Testing connection string method...');
    const client1 = await Promise.race([
      pool1.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout')), 30000)
      )
    ]);

    const result1 = await client1.query('SELECT version()');
    console.log('✅ Method 1 SUCCESS:', result1.rows[0].version);
    client1.release();
    await pool1.end();
    return true;

  } catch (error) {
    console.log('❌ Method 1 FAILED:', error.message);
  }

  // Method 2: Using individual parameters with ssl: false
  console.log('\n📋 Method 2: Individual Parameters with ssl: false');
  try {
    const pool2 = new Pool({
      user: 'sgpostgres',
      password: 'd3tnRHMiuiNI+OTc',
      host: 'SG-rough-guava-317-7201-pgsql-master.servers.mongodirector.com',
      port: 6432,
      database: 'postgres',
      ssl: false,
      connectionTimeoutMillis: 30000,
      idleTimeoutMillis: 30000,
      max: 1
    });

    console.log('🔄 Testing individual parameters method...');
    const client2 = await Promise.race([
      pool2.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout')), 30000)
      )
    ]);

    const result2 = await client2.query('SELECT version()');
    console.log('✅ Method 2 SUCCESS:', result2.rows[0].version);
    client2.release();
    await pool2.end();
    return true;

  } catch (error) {
    console.log('❌ Method 2 FAILED:', error.message);
  }

  // Method 3: Using connection string without sslmode parameter
  console.log('\n📋 Method 3: Connection String without sslmode');
  const connectionString3 = "postgresql://sgpostgres:<EMAIL>:6432/postgres";
  
  try {
    const pool3 = new Pool({
      connectionString: connectionString3,
      ssl: false,
      connectionTimeoutMillis: 30000,
      idleTimeoutMillis: 30000,
      max: 1
    });

    console.log('🔄 Testing connection string without sslmode...');
    const client3 = await Promise.race([
      pool3.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout')), 30000)
      )
    ]);

    const result3 = await client3.query('SELECT version()');
    console.log('✅ Method 3 SUCCESS:', result3.rows[0].version);
    client3.release();
    await pool3.end();
    return true;

  } catch (error) {
    console.log('❌ Method 3 FAILED:', error.message);
  }

  return false;
}

// Run the test
testScaleGridConnection()
  .then(success => {
    if (success) {
      console.log('\n🎉 SUCCESS: At least one connection method worked!');
      console.log('✅ ScaleGrid PostgreSQL is accessible from this machine.');
      process.exit(0);
    } else {
      console.log('\n💥 FAILURE: All connection methods failed.');
      console.log('❌ ScaleGrid PostgreSQL is not accessible from this machine.');
      console.log('\n🔍 Possible issues:');
      console.log('   1. ScaleGrid instance is down or not responding');
      console.log('   2. IP whitelist not properly configured');
      console.log('   3. Network/firewall blocking the connection');
      console.log('   4. Incorrect credentials or database name');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
