"""
COC Router Service

Central routing and orchestration hub that:
- Routes requests to appropriate Gemini models
- Coordinates multi-service operations
- Manages execution flow and dependencies
- Provides unified response handling
- Integrates with existing LLM service infrastructure
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import aiohttp

logger = logging.getLogger(__name__)

@dataclass
class ExecutionStep:
    id: str
    service: str
    method: str
    parameters: Dict[str, Any]
    dependencies: List[str]
    timeout: int
    retries: int
    priority: int

@dataclass
class ExecutionPlan:
    id: str
    steps: List[ExecutionStep]
    estimated_duration: int
    dependencies: List[str]
    parallelizable: bool

class COCRouter:
    """Central routing and orchestration hub for COC requests"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.COCRouter")
        self.execution_history: Dict[str, Dict[str, Any]] = {}
        self.performance_metrics: Dict[str, List[Dict[str, Any]]] = {}
        
    async def initialize(self) -> None:
        """Initialize COC Router"""
        self.logger.info("Initializing COC Router...")
        
        try:
            # Initialize performance tracking
            self._initialize_performance_tracking()
            
            self.logger.info("COC Router initialized successfully")
            
        except Exception as error:
            self.logger.error(f"COC Router initialization failed: {error}")
            raise

    async def execute_request(
        self,
        request,
        intent_classification,
        platform_capabilities: List[Any],
        selected_model: str
    ) -> Dict[str, Any]:
        """Execute a request based on intent classification and platform capabilities"""
        start_time = time.time()
        
        try:
            self.logger.info("Executing COC request", extra={
                'request_id': request.id,
                'intent': intent_classification.intent,
                'complexity': intent_classification.complexity,
                'model': selected_model
            })

            # Create execution plan based on intent
            execution_plan = await self._create_execution_plan(
                request,
                intent_classification,
                platform_capabilities,
                selected_model
            )

            # Execute the plan
            result = await self._execute_plan(execution_plan, request, selected_model)

            # Record performance metrics
            self._record_performance_metrics(
                intent_classification.intent,
                int((time.time() - start_time) * 1000),
                result['success']
            )

            # Store execution history
            self.execution_history[request.id] = result

            return result

        except Exception as error:
            self.logger.error(f"Request execution failed: {error}", extra={
                'request_id': request.id
            })

            return {
                'success': False,
                'confidence': 0,
                'error': str(error),
                'metadata': {
                    'execution_time': int((time.time() - start_time) * 1000),
                    'intent': intent_classification.intent
                }
            }

    async def _create_execution_plan(
        self,
        request,
        intent_classification,
        platform_capabilities: List[Any],
        selected_model: str
    ) -> ExecutionPlan:
        """Create an execution plan based on intent and capabilities"""
        
        plan_id = f"plan_{request.id}_{int(time.time() * 1000)}"
        steps: List[ExecutionStep] = []

        # Route based on intent classification
        intent = intent_classification.intent
        
        if intent == 'create_campaign':
            steps.extend(await self._create_campaign_steps(request, intent_classification, selected_model))
        elif intent == 'analyze_performance':
            steps.extend(await self._create_analysis_steps(request, intent_classification, selected_model))
        elif intent == 'generate_content':
            steps.extend(await self._create_content_generation_steps(request, intent_classification, selected_model))
        elif intent == 'manage_accounts':
            steps.extend(await self._create_account_management_steps(request, intent_classification, selected_model))
        elif intent == 'automation_control':
            steps.extend(await self._create_automation_steps(request, intent_classification, selected_model))
        elif intent == 'get_insights':
            steps.extend(await self._create_insight_steps(request, intent_classification, selected_model))
        elif intent == 'system_status':
            steps.extend(await self._create_status_steps(request, intent_classification, selected_model))
        else:
            # Fallback to general AI response
            steps.extend(await self._create_general_response_steps(request, intent_classification, selected_model))

        # Calculate estimated duration
        estimated_duration = sum(step.timeout for step in steps)

        # Determine dependencies
        dependencies = list(set(dep for step in steps for dep in step.dependencies))

        # Check if steps can be parallelized
        parallelizable = all(len(step.dependencies) == 0 for step in steps)

        return ExecutionPlan(
            id=plan_id,
            steps=steps,
            estimated_duration=estimated_duration,
            dependencies=dependencies,
            parallelizable=parallelizable
        )

    async def _execute_plan(
        self,
        plan: ExecutionPlan,
        request,
        selected_model: str
    ) -> Dict[str, Any]:
        """Execute an execution plan"""
        
        self.logger.debug("Executing plan", extra={
            'plan_id': plan.id,
            'step_count': len(plan.steps),
            'parallelizable': plan.parallelizable
        })

        results: Dict[str, Any] = {}
        actions: List[Dict[str, Any]] = []
        overall_confidence = 1.0

        try:
            # For Phase 1, we primarily execute through Gemini models
            for step in plan.steps:
                try:
                    step_result = await self._execute_step(step, request, selected_model)
                    results[step.id] = step_result
                    overall_confidence *= step_result.get('confidence', 0.8)
                    
                    # Add to actions list
                    actions.append({
                        'type': step.method,
                        'service': step.service,
                        'method': step.method,
                        'parameters': step.parameters,
                        'priority': step.priority,
                        'estimated_duration': step.timeout,
                        'dependencies': step.dependencies
                    })
                    
                except Exception as error:
                    self.logger.error(f"Step execution failed: {error}", extra={
                        'step_id': step.id
                    })
                    overall_confidence *= 0.5

            # Compile final response
            content = self._compile_response(results, request, plan)

            return {
                'success': True,
                'content': content,
                'actions': actions,
                'confidence': max(0.1, overall_confidence),
                'metadata': {
                    'plan_id': plan.id,
                    'step_count': len(plan.steps),
                    'execution_mode': 'parallel' if plan.parallelizable else 'sequential',
                    'results': results
                }
            }

        except Exception as error:
            return {
                'success': False,
                'confidence': 0,
                'error': str(error),
                'metadata': {
                    'plan_id': plan.id,
                    'step_count': len(plan.steps)
                }
            }

    async def _execute_step(
        self,
        step: ExecutionStep,
        request,
        selected_model: str
    ) -> Dict[str, Any]:
        """Execute a single step"""
        
        start_time = time.time()
        
        try:
            self.logger.debug("Executing step", extra={
                'step_id': step.id,
                'service': step.service,
                'method': step.method,
                'model': selected_model
            })

            # For Phase 1, most steps go through Gemini models
            if step.service == 'gemini':
                result = await self._execute_gemini_step(step, request, selected_model)
            elif step.service == 'analysis':
                result = await self._execute_analysis_step(step, request, selected_model)
            elif step.service == 'content':
                result = await self._execute_content_step(step, request, selected_model)
            else:
                result = await self._execute_generic_step(step, request, selected_model)

            execution_time = int((time.time() - start_time) * 1000)
            
            self.logger.debug("Step executed successfully", extra={
                'step_id': step.id,
                'service': step.service,
                'method': step.method,
                'execution_time': execution_time
            })

            return {
                **result,
                'execution_time': execution_time,
                'confidence': result.get('confidence', 0.8)
            }

        except Exception as error:
            execution_time = int((time.time() - start_time) * 1000)
            
            self.logger.error(f"Step execution failed: {error}", extra={
                'step_id': step.id,
                'service': step.service,
                'method': step.method,
                'execution_time': execution_time
            })

            raise error

    async def _execute_gemini_step(
        self,
        step: ExecutionStep,
        request,
        selected_model: str
    ) -> Dict[str, Any]:
        """Execute Gemini model step"""
        
        try:
            # Prepare the prompt based on step parameters
            enhanced_prompt = request.input
            
            # Add context based on step type
            if step.parameters.get('context_prefix'):
                enhanced_prompt = f"{step.parameters['context_prefix']}\n\n{enhanced_prompt}"
            
            if step.parameters.get('instructions'):
                enhanced_prompt = f"{enhanced_prompt}\n\nInstructions: {step.parameters['instructions']}"

            # Import and use the existing Gemini client
            from ..gemini.gemini_client import GeminiClient, GeminiRequest, GeminiModel

            gemini_client = GeminiClient()

            # Map model name to GeminiModel enum
            model_mapping = {
                'gemini-2.5-pro': GeminiModel.PRO_2_5,
                'gemini-2.5-flash': GeminiModel.FLASH_2_5,
                'gemini-2.5-flash-lite': GeminiModel.FLASH_2_5  # Use Flash as fallback for lite
            }

            gemini_model = model_mapping.get(selected_model, GeminiModel.FLASH_2_5)

            # Create GeminiRequest object
            gemini_request = GeminiRequest(
                prompt=enhanced_prompt,
                model=gemini_model,
                temperature=step.parameters.get('temperature', 0.7),
                max_tokens=step.parameters.get('max_tokens', 1000),
                top_p=step.parameters.get('top_p'),
                top_k=step.parameters.get('top_k')
            )

            # Call Gemini through the existing client
            response = await gemini_client.generate_content(gemini_request)

            return {
                'content': response.content,
                'confidence': response.confidence_score if response.confidence_score > 0 else 0.8,
                'model': selected_model,
                'usage': response.usage,
                'metadata': {
                    'step_type': step.method,
                    'original_prompt': request.input,
                    'enhanced_prompt': enhanced_prompt,
                    'response_time': response.response_time,
                    'tokens_used': response.tokens_used,
                    'quality_score': response.quality_score
                }
            }

        except Exception as error:
            self.logger.error(f"Gemini step execution failed: {error}", extra={
                'step_id': step.id,
                'model': selected_model
            })
            raise error

    def _compile_response(
        self,
        results: Dict[str, Any],
        request,
        plan: ExecutionPlan
    ) -> str:
        """Compile final response from step results"""
        
        # Find the primary content result
        for result in results.values():
            if result.get('content'):
                return result['content']

        # Fallback: create a summary of actions taken
        action_summary = ', '.join(f"{step_id}: {result.get('status', 'completed')}" 
                                  for step_id, result in results.items())

        return f"Request processed successfully through COC. Actions taken: {action_summary}"

    # Step creation methods for different intents
    async def _create_campaign_steps(self, request, intent, model: str) -> List[ExecutionStep]:
        return [ExecutionStep(
            id='gemini_campaign_creation',
            service='gemini',
            method='strategic_planning',
            parameters={
                'context_prefix': 'You are an expert marketing strategist. Create a comprehensive campaign strategy.',
                'temperature': 0.7,
                'max_tokens': 2000,
                'instructions': 'Focus on target audience, messaging, channels, and success metrics.'
            },
            dependencies=[],
            timeout=30000,
            retries=2,
            priority=1
        )]

    async def _create_analysis_steps(self, request, intent, model: str) -> List[ExecutionStep]:
        return [ExecutionStep(
            id='gemini_performance_analysis',
            service='gemini',
            method='data_analysis',
            parameters={
                'context_prefix': 'You are a data analyst. Analyze the performance metrics and provide insights.',
                'temperature': 0.3,
                'max_tokens': 1500,
                'instructions': 'Provide specific recommendations based on the data.'
            },
            dependencies=[],
            timeout=20000,
            retries=2,
            priority=1
        )]

    async def _create_content_generation_steps(self, request, intent, model: str) -> List[ExecutionStep]:
        return [ExecutionStep(
            id='gemini_content_generation',
            service='gemini',
            method='creative_writing',
            parameters={
                'context_prefix': 'You are a creative content writer. Generate engaging content.',
                'temperature': 0.8,
                'max_tokens': 1000,
                'instructions': 'Make it engaging, on-brand, and optimized for the target platform.'
            },
            dependencies=[],
            timeout=25000,
            retries=2,
            priority=1
        )]

    async def _create_account_management_steps(self, request, intent, model: str) -> List[ExecutionStep]:
        return [ExecutionStep(
            id='gemini_account_guidance',
            service='gemini',
            method='guidance',
            parameters={
                'context_prefix': 'You are a social media account management expert. Provide guidance.',
                'temperature': 0.5,
                'max_tokens': 800,
                'instructions': 'Give clear, actionable steps for account management.'
            },
            dependencies=[],
            timeout=15000,
            retries=2,
            priority=1
        )]

    async def _create_automation_steps(self, request, intent, model: str) -> List[ExecutionStep]:
        return [ExecutionStep(
            id='gemini_automation_guidance',
            service='gemini',
            method='technical_guidance',
            parameters={
                'context_prefix': 'You are an automation expert. Provide technical guidance.',
                'temperature': 0.4,
                'max_tokens': 1200,
                'instructions': 'Focus on best practices, safety, and compliance.'
            },
            dependencies=[],
            timeout=20000,
            retries=2,
            priority=1
        )]

    async def _create_insight_steps(self, request, intent, model: str) -> List[ExecutionStep]:
        return [ExecutionStep(
            id='gemini_insights_generation',
            service='gemini',
            method='strategic_analysis',
            parameters={
                'context_prefix': 'You are a strategic advisor. Provide deep insights and recommendations.',
                'temperature': 0.6,
                'max_tokens': 1800,
                'instructions': 'Provide actionable insights with supporting reasoning.'
            },
            dependencies=[],
            timeout=25000,
            retries=2,
            priority=1
        )]

    async def _create_status_steps(self, request, intent, model: str) -> List[ExecutionStep]:
        return [ExecutionStep(
            id='gemini_status_summary',
            service='gemini',
            method='system_summary',
            parameters={
                'context_prefix': 'You are a system administrator. Provide a clear status summary.',
                'temperature': 0.2,
                'max_tokens': 500,
                'instructions': 'Be concise and informative about system status.'
            },
            dependencies=[],
            timeout=10000,
            retries=1,
            priority=1
        )]

    async def _create_general_response_steps(self, request, intent, model: str) -> List[ExecutionStep]:
        return [ExecutionStep(
            id='gemini_general_response',
            service='gemini',
            method='general_assistance',
            parameters={
                'context_prefix': 'You are a helpful AI assistant.',
                'temperature': 0.7,
                'max_tokens': 1000,
                'instructions': 'Provide a helpful and informative response.'
            },
            dependencies=[],
            timeout=20000,
            retries=2,
            priority=1
        )]

    # Execution methods for different step types
    async def _execute_analysis_step(self, step: ExecutionStep, request, model: str) -> Dict[str, Any]:
        return await self._execute_gemini_step(step, request, model)

    async def _execute_content_step(self, step: ExecutionStep, request, model: str) -> Dict[str, Any]:
        return await self._execute_gemini_step(step, request, model)

    async def _execute_generic_step(self, step: ExecutionStep, request, model: str) -> Dict[str, Any]:
        return await self._execute_gemini_step(step, request, model)

    def _initialize_performance_tracking(self) -> None:
        """Initialize performance tracking"""
        # Set up periodic cleanup of old metrics
        async def cleanup_metrics():
            while True:
                try:
                    await asyncio.sleep(3600)  # Clean up every hour
                    cutoff = time.time() - (24 * 60 * 60)  # 24 hours
                    
                    for intent, metrics in self.performance_metrics.items():
                        filtered = [m for m in metrics if m['timestamp'] > cutoff]
                        self.performance_metrics[intent] = filtered
                except Exception as error:
                    self.logger.error(f"Metrics cleanup failed: {error}")
        
        asyncio.create_task(cleanup_metrics())

    def _record_performance_metrics(
        self,
        intent: str,
        execution_time: int,
        success: bool
    ) -> None:
        """Record performance metrics"""
        
        if intent not in self.performance_metrics:
            self.performance_metrics[intent] = []

        metrics = self.performance_metrics[intent]
        metrics.append({
            'timestamp': time.time(),
            'execution_time': execution_time,
            'success': success,
            'intent': intent
        })

        # Keep only last 1000 metrics per intent
        if len(metrics) > 1000:
            self.performance_metrics[intent] = metrics[-1000:]

    def get_status(self) -> Dict[str, Any]:
        """Get router status"""
        performance_summary = {}
        for intent, metrics in self.performance_metrics.items():
            if metrics:
                avg_time = sum(m['execution_time'] for m in metrics) / len(metrics)
                success_rate = sum(1 for m in metrics if m['success']) / len(metrics)
                performance_summary[intent] = {
                    'count': len(metrics),
                    'average_time': avg_time,
                    'success_rate': success_rate
                }
        
        return {
            'execution_history_size': len(self.execution_history),
            'performance_metrics': performance_summary
        }

    async def shutdown(self) -> None:
        """Shutdown COC Router"""
        self.logger.info("COC Router shutting down...")
        self.execution_history.clear()
        self.performance_metrics.clear()
