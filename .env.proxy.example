# =============================================================================
# TWIKIT ENTERPRISE PROXY CONFIGURATION
# =============================================================================
# This file contains comprehensive proxy configuration for the Twikit integration
# Copy this to .env and configure with your actual proxy provider details

# =============================================================================
# PROXY ROTATION SETTINGS
# =============================================================================
TWIKIT_ENABLE_PROXY_ROTATION=true
TWIKIT_PROXY_ROTATION_INTERVAL=300
TWIKIT_PROXY_HEALTH_CHECK_INTERVAL=60
TWIKIT_PROXY_MAX_FAILURES=5
TWIKIT_PROXY_HEALTH_CHECK_TIMEOUT=10

# =============================================================================
# RESIDENTIAL PROXY POOL
# =============================================================================
# High-quality residential proxies for maximum anti-detection
TWIKIT_RESIDENTIAL_PROXY_ENABLED=false
TWIKIT_RESIDENTIAL_PROXY_URLS=""
TWIKIT_RESIDENTIAL_PROXY_USERNAME=""
TWIKIT_RESIDENTIAL_PROXY_PASSWORD=""

# Example configuration for top-rated providers (2024 research):

# BrightData (Luminati) - Best Overall for Enterprise
# Largest proxy network with 72M+ residential IPs
# TWIKIT_RESIDENTIAL_PROXY_URLS="http://brd-customer-hl_12345678-zone-residential:8080"
# TWIKIT_RESIDENTIAL_PROXY_USERNAME="brd-customer-hl_12345678-zone-residential"
# TWIKIT_RESIDENTIAL_PROXY_PASSWORD="your_password"

# SmartProxy - Best Value for Mid-Tier
# 65M+ residential IPs with sticky sessions
# TWIKIT_RESIDENTIAL_PROXY_URLS="http://gate.smartproxy.com:10000,http://gate.smartproxy.com:10001"
# TWIKIT_RESIDENTIAL_PROXY_USERNAME="your_username"
# TWIKIT_RESIDENTIAL_PROXY_PASSWORD="your_password"

# Oxylabs - Premium Enterprise Solution
# 100M+ residential IPs with advanced features
# TWIKIT_RESIDENTIAL_PROXY_URLS="http://pr.oxylabs.io:7777"
# TWIKIT_RESIDENTIAL_PROXY_USERNAME="customer-your_username"
# TWIKIT_RESIDENTIAL_PROXY_PASSWORD="your_password"

# NetNut - Best for Mobile Proxies
# Real mobile carrier IPs for maximum authenticity
# TWIKIT_MOBILE_PROXY_URLS="http://gw.netnut.io:5959"
# TWIKIT_MOBILE_PROXY_USERNAME="your_username"
# TWIKIT_MOBILE_PROXY_PASSWORD="your_password"

# =============================================================================
# DATACENTER PROXY POOL
# =============================================================================
# Fast datacenter proxies for high-volume operations
TWIKIT_DATACENTER_PROXY_ENABLED=false
TWIKIT_DATACENTER_PROXY_URLS=""
TWIKIT_DATACENTER_PROXY_USERNAME=""
TWIKIT_DATACENTER_PROXY_PASSWORD=""

# Example configuration for datacenter providers:
# BrightData Datacenter:
# TWIKIT_DATACENTER_PROXY_URLS="http://brd-customer-hl_12345678-zone-datacenter:8080"
# TWIKIT_DATACENTER_PROXY_USERNAME="brd-customer-hl_12345678-zone-datacenter"
# TWIKIT_DATACENTER_PROXY_PASSWORD="your_password"

# ProxyMesh:
# TWIKIT_DATACENTER_PROXY_URLS="http://us-wa.proxymesh.com:31280,http://us-ca.proxymesh.com:31280"
# TWIKIT_DATACENTER_PROXY_USERNAME="your_username"
# TWIKIT_DATACENTER_PROXY_PASSWORD="your_password"

# =============================================================================
# MOBILE PROXY POOL
# =============================================================================
# Mobile carrier proxies for maximum authenticity
TWIKIT_MOBILE_PROXY_ENABLED=false
TWIKIT_MOBILE_PROXY_URLS=""
TWIKIT_MOBILE_PROXY_USERNAME=""
TWIKIT_MOBILE_PROXY_PASSWORD=""

# Example configuration for mobile providers:
# BrightData Mobile:
# TWIKIT_MOBILE_PROXY_URLS="http://brd-customer-hl_12345678-zone-mobile:8080"
# TWIKIT_MOBILE_PROXY_USERNAME="brd-customer-hl_12345678-zone-mobile"
# TWIKIT_MOBILE_PROXY_PASSWORD="your_password"

# =============================================================================
# PROXY HEALTH CHECK CONFIGURATION
# =============================================================================
# URLs used to test proxy health and connectivity
TWIKIT_PROXY_HEALTH_CHECK_URLS="https://httpbin.org/ip,https://api.ipify.org?format=json,https://ifconfig.me/ip"

# =============================================================================
# SCRAPER API INTEGRATION
# =============================================================================
# Use scraper APIs as proxy providers for enhanced anti-detection

# Enable scraper API usage
USE_SCRAPER_API=true
FALLBACK_TO_TRADITIONAL_PROXIES=true

# ScraperAPI Configuration (Your provided API key)
SCRAPER_API_KEY="312ba5e97b195f504b88233282dc07b8"
SCRAPER_API_ENDPOINT="http://api.scraperapi.com"
SCRAPER_API_PROXY_ENDPOINT="proxy-server.scraperapi.com:8001"

# ZenRows Configuration (Alternative)
ZENROWS_API_KEY=""
ZENROWS_ENDPOINT="https://api.zenrows.com/v1"
ZENROWS_PROXY_ENDPOINT="proxy.zenrows.com:8001"

# Oxylabs Scraper API Configuration
OXYLABS_API_KEY=""
OXYLABS_ENDPOINT="https://realtime.oxylabs.io/v1/queries"
OXYLABS_PROXY_ENDPOINT="realtime.oxylabs.io:60000"

# BrightData Scraper API Configuration
BRIGHTDATA_API_KEY=""
BRIGHTDATA_ENDPOINT="https://api.brightdata.com"
BRIGHTDATA_PROXY_ENDPOINT="brd.superproxy.io:22225"

# =============================================================================
# ADVANCED PROXY SETTINGS
# =============================================================================
# Additional proxy configuration for fine-tuning

# Proxy timeout settings (in seconds)
TWIKIT_PROXY_CONNECT_TIMEOUT=10
TWIKIT_PROXY_READ_TIMEOUT=30

# Retry configuration
TWIKIT_PROXY_MAX_RETRIES=3
TWIKIT_PROXY_RETRY_DELAY=1000

# Session management
TWIKIT_PROXY_STICKY_SESSION_ENABLED=true
TWIKIT_PROXY_STICKY_SESSION_DURATION=1800

# Geographic targeting
TWIKIT_PROXY_PREFERRED_COUNTRIES="US,CA,GB,AU"
TWIKIT_PROXY_BLOCKED_COUNTRIES="CN,RU,IR,KP"

# Performance thresholds
TWIKIT_PROXY_MIN_SUCCESS_RATE=0.8
TWIKIT_PROXY_MAX_RESPONSE_TIME=5000

# =============================================================================
# PROXY PROVIDER SPECIFIC SETTINGS
# =============================================================================

# BrightData specific settings
BRIGHTDATA_CUSTOMER_ID=""
BRIGHTDATA_ZONE_PASSWORD=""

# SmartProxy specific settings
SMARTPROXY_ENDPOINT=""
SMARTPROXY_PORT=10000

# Oxylabs specific settings
OXYLABS_ENDPOINT="pr.oxylabs.io"
OXYLABS_PORT=7777

# =============================================================================
# DEVELOPMENT/TESTING PROXY SETTINGS
# =============================================================================
# For development and testing purposes

# Enable demo proxies for development
TWIKIT_ENABLE_DEMO_PROXIES=true

# Local proxy for testing (if you have one)
TWIKIT_LOCAL_PROXY_HOST=""
TWIKIT_LOCAL_PROXY_PORT=8080
TWIKIT_LOCAL_PROXY_USERNAME=""
TWIKIT_LOCAL_PROXY_PASSWORD=""

# =============================================================================
# PROXY MONITORING AND ANALYTICS
# =============================================================================

# Enable detailed proxy logging
TWIKIT_PROXY_DETAILED_LOGGING=true

# Proxy performance monitoring
TWIKIT_PROXY_PERFORMANCE_MONITORING=true

# Proxy usage analytics
TWIKIT_PROXY_USAGE_ANALYTICS=true

# Alert thresholds
TWIKIT_PROXY_ALERT_FAILURE_RATE=0.3
TWIKIT_PROXY_ALERT_RESPONSE_TIME=10000

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Proxy credential encryption
TWIKIT_PROXY_ENCRYPT_CREDENTIALS=true

# IP rotation frequency (in minutes)
TWIKIT_PROXY_IP_ROTATION_FREQUENCY=15

# User agent rotation
TWIKIT_PROXY_ROTATE_USER_AGENTS=true

# =============================================================================
# COST OPTIMIZATION
# =============================================================================

# Cost tracking and limits
TWIKIT_PROXY_COST_TRACKING=true
TWIKIT_PROXY_MONTHLY_BUDGET=100.00
TWIKIT_PROXY_COST_ALERT_THRESHOLD=80.00

# Usage optimization
TWIKIT_PROXY_OPTIMIZE_USAGE=true
TWIKIT_PROXY_PREFER_CHEAPER_POOLS=false

# =============================================================================
# FAILOVER AND REDUNDANCY
# =============================================================================

# Failover strategy: immediate, gradual, circuit_breaker
TWIKIT_PROXY_FAILOVER_STRATEGY=gradual

# Load balancing algorithm: round_robin, least_connections, weighted, ip_hash
TWIKIT_PROXY_LOAD_BALANCING=weighted

# Circuit breaker settings
TWIKIT_PROXY_CIRCUIT_BREAKER_THRESHOLD=5
TWIKIT_PROXY_CIRCUIT_BREAKER_TIMEOUT=300

# =============================================================================
# COMPLIANCE AND LEGAL
# =============================================================================

# Respect robots.txt
TWIKIT_PROXY_RESPECT_ROBOTS_TXT=true

# Rate limiting compliance
TWIKIT_PROXY_ENFORCE_RATE_LIMITS=true

# Geographic compliance
TWIKIT_PROXY_GEOGRAPHIC_COMPLIANCE=true

# =============================================================================
# NOTES AND RECOMMENDATIONS
# =============================================================================

# 1. For production use, enable at least one proxy pool (residential recommended)
# 2. Use residential proxies for account creation and sensitive operations
# 3. Use datacenter proxies for high-volume data collection
# 4. Mobile proxies provide the highest authenticity but are more expensive
# 5. Always test proxy configurations before production deployment
# 6. Monitor proxy performance and costs regularly
# 7. Rotate proxies frequently to avoid detection
# 8. Keep proxy credentials secure and encrypted
# 9. Comply with proxy provider terms of service
# 10. Respect target website terms of service and rate limits

# =============================================================================
# QUICK SETUP GUIDE
# =============================================================================

# 1. Copy this file to .env in your backend directory
# 2. Configure at least one proxy pool with your provider details
# 3. Set TWIKIT_ENABLE_PROXY_ROTATION=true
# 4. Run: npm run proxy:setup
# 5. Check the setup report for any issues
# 6. Test with: npm run proxy:test

# For demo/development setup:
# 1. Set TWIKIT_ENABLE_DEMO_PROXIES=true
# 2. Run: npm run proxy:setup
# 3. This will create demo proxy configurations for testing
