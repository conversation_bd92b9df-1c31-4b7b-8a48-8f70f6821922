{"name": "x-marketing-backend", "version": "1.0.0", "description": "Backend API for X Marketing Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "dev:services": "npm run services:start && npm run dev", "dev:auto": "node scripts/start-with-services.js", "dev:full": "node scripts/start-with-services.js", "build": "npm run setup:python && tsc", "build:prod": "npm run setup:python:prod && tsc", "build:clean": "npm run clean && npm run build", "start": "node dist/index.js", "start:production": "NODE_ENV=production node dist/index.js", "services:start": "docker-compose -f ../docker-compose.local.yml up -d postgres redis", "services:stop": "docker-compose -f ../docker-compose.local.yml stop postgres redis", "services:restart": "npm run services:stop && npm run services:start", "services:logs": "docker-compose -f ../docker-compose.local.yml logs -f postgres redis", "test": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:watch": "jest --watch", "test:coverage": "jest --coverage --coverageReporters=text --coverageReporters=html --coverageReporters=lcov", "test:coverage:detailed": "jest --coverage --coverageReporters=text --coverageReporters=html --coverageReporters=lcov --coverageReporters=json --coverageReporters=cobertura", "test:unit": "TEST_TYPE=unit jest --testPathPattern=unit --coverage", "test:unit:watch": "TEST_TYPE=unit jest --testPathPattern=unit --watch", "test:unit:verbose": "TEST_TYPE=unit jest --testPathPattern=unit --verbose --coverage", "test:integration": "TEST_TYPE=integration jest --testPathPattern=integration --runInBand", "test:integration:watch": "TEST_TYPE=integration jest --testPathPattern=integration --watch --runInBand", "test:integration:verbose": "TEST_TYPE=integration jest --testPathPattern=integration --verbose --runInBand", "test:e2e": "TEST_TYPE=e2e jest --testPathPattern=e2e --runInBand --forceExit", "test:e2e:watch": "TEST_TYPE=e2e jest --testPathPattern=e2e --watch --runInBand", "test:e2e:verbose": "TEST_TYPE=e2e jest --testPathPattern=e2e --verbose --runInBand --forceExit", "test:performance": "TEST_TYPE=performance jest --testPathPattern=performance --runInBand --forceExit --testTimeout=300000", "test:performance:verbose": "TEST_TYPE=performance jest --testPathPattern=performance --verbose --runInBand --forceExit --testTimeout=300000", "test:security": "TEST_TYPE=security jest --testPathPattern=security --runInBand --forceExit", "test:security:verbose": "TEST_TYPE=security jest --testPathPattern=security --verbose --runInBand --forceExit", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e && npm run test:performance && npm run test:security", "test:all:parallel": "concurrently \"npm run test:unit\" \"npm run test:integration\" \"npm run test:security\"", "test:ci": "npm run test:unit && npm run test:integration && npm run test:e2e && npm run test:security", "test:quick": "TEST_TYPE=unit jest --testPathPattern=unit --passWithNoTests", "test:setup": "npm run test:db:setup && npm run test:redis:setup", "test:cleanup": "npm run test:db:cleanup && npm run test:redis:cleanup", "test:db:setup": "createdb x_marketing_test || true && DATABASE_URL=$TEST_DATABASE_URL npx prisma migrate deploy", "test:db:cleanup": "dropdb x_marketing_test || true", "test:redis:setup": "redis-cli -n 1 flushdb || true", "test:redis:cleanup": "redis-cli -n 1 flushdb || true", "test:load": "artillery run load-test.yml", "db:migrate": "npx prisma migrate dev", "db:migrate:prod": "npx prisma migrate deploy", "db:generate": "npx prisma generate", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio", "db:reset": "npx prisma migrate reset --force", "proxy:seed": "npx tsx scripts/seed-proxy-configurations.ts", "proxy:setup": "npx tsx scripts/setup-proxy-services.ts", "proxy:test": "npm run proxy:setup", "proxy:recommendations": "npx tsx scripts/enhanced-proxy-recommendations.ts", "scraper-api:test": "npx tsx scripts/test-scraper-api-integration.ts", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "lint:test": "eslint __tests__ --ext .ts", "lint:test:fix": "eslint __tests__ --ext .ts --fix", "lint:all": "eslint src __tests__ --ext .ts", "lint:all:fix": "eslint src __tests__ --ext .ts --fix", "type-check": "tsc --noEmit", "type-check:test": "tsc --noEmit --project __tests__/tsconfig.json", "quality:check": "npm run lint && npm run test:coverage && npm run test:security", "quality:fix": "npm run lint:fix && npm run test", "quality:report": "npm run test:coverage:detailed && npm run lint > quality-report.txt", "benchmark": "npm run test:performance", "benchmark:memory": "node --expose-gc --max-old-space-size=4096 node_modules/.bin/jest --testPathPattern=performance --logHeapUsage", "benchmark:cpu": "node --prof node_modules/.bin/jest --testPathPattern=performance --runInBand", "security:audit": "npm audit", "security:fix": "npm audit fix", "security:test": "npm run test:security", "health:check": "curl -f http://localhost:3001/health/ready || exit 1", "metrics:export": "curl http://localhost:3001/metrics/prometheus", "docs:test": "typedoc --out docs/tests __tests__", "docs:coverage": "open coverage/lcov-report/index.html || xdg-open coverage/lcov-report/index.html", "test:hardening": "node test-hardened-backend.js", "test:services": "ts-node tests/serviceRouting/runTests.ts", "test:services:basic": "ts-node tests/serviceRouting/runTests.ts basic", "test:services:routing": "ts-node tests/serviceRouting/runTests.ts routing", "test:services:integration": "ts-node tests/serviceRouting/runTests.ts integration", "test:services:all": "ts-node tests/serviceRouting/runTests.ts all", "test:services:health": "ts-node tests/serviceRouting/runTests.ts --health", "test:services:status": "ts-node tests/serviceRouting/runTests.ts --status", "start:services": "node scripts/startServices.js", "start:services:dev": "ENABLE_ALL_SERVICES=true SERVICE_STARTUP_MODE=comprehensive npm run dev", "setup:python": "node scripts/test-python.js", "setup:python:prod": "node scripts/test-python.js --skip-verify", "setup:python:force": "node scripts/test-python.js --force", "setup:python:quick": "node scripts/test-python.js --skip-verify", "setup:python:check": "node scripts/check-python-env.js", "python:install": "python_env/Scripts/pip.exe install -r requirements-python.txt", "python:install:unix": "python_env/bin/pip install -r requirements-python.txt", "python:test": "python_env/Scripts/python.exe -c \"import twikit; print('Twikit version:', twikit.__version__)\"", "python:test:unix": "python_env/bin/python -c \"import twikit; print('Twikit version:', twikit.__version__)\"", "python:activate": "python_env/Scripts/activate.bat", "python:activate:unix": "source python_env/bin/activate", "python:clean": "rimraf python_env requirements-python.txt", "twikit:test": "node scripts/test-twikit-integration.js", "twikit:auth": "node scripts/twikit-auth-test.js", "clean": "rimraf dist python_env requirements-python.txt *.py"}, "dependencies": {"@opentelemetry/api": "^1.8.0", "@opentelemetry/auto-instrumentations-node": "^0.46.1", "@opentelemetry/exporter-jaeger": "^1.24.1", "@opentelemetry/exporter-prometheus": "^0.51.1", "@opentelemetry/instrumentation-express": "^0.39.0", "@opentelemetry/instrumentation-http": "^0.51.1", "@opentelemetry/instrumentation-pg": "^0.42.0", "@opentelemetry/instrumentation-redis": "^0.40.0", "@opentelemetry/resources": "^1.24.1", "@opentelemetry/sdk-metrics": "^1.24.1", "@opentelemetry/sdk-node": "^0.51.1", "@opentelemetry/sdk-trace-node": "^1.24.1", "@opentelemetry/semantic-conventions": "^1.24.1", "@prisma/client": "^6.11.1", "@types/cookie-parser": "^1.4.9", "@types/express-session": "^1.18.2", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "agenda": "^5.0.0", "archiver": "^6.0.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bull": "^4.12.0", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4", "connect-redis": "^7.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dayjs": "^1.11.10", "dotenv": "^16.3.1", "etcd3": "^1.1.2", "express": "^4.18.2", "express-brute": "^1.0.1", "express-brute-redis": "^0.0.1", "express-rate-limit": "^7.5.1", "express-session": "^1.17.3", "express-slow-down": "^2.1.0", "express-validator": "^7.0.1", "graceful-fs": "^4.2.11", "helmet": "^7.2.0", "ioredis": "^5.3.2", "jimp": "^0.22.10", "joi": "^17.11.0", "jsdom": "^23.0.1", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "knex": "^3.1.0", "lodash": "^4.17.21", "lru-cache": "^10.1.0", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^2.0.0", "mysql2": "^3.6.5", "node-cron": "^3.0.3", "node-schedule": "^2.1.1", "objection": "^3.1.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdf2pic": "^3.1.1", "pg": "^8.11.3", "pg-mem": "^3.0.5", "prom-client": "^15.0.0", "proxy-agent": "^6.3.1", "puppeteer": "^24.14.0", "puppeteer-core": "^24.14.0", "qrcode": "^1.5.4", "rate-limit-redis": "^4.2.1", "rate-limiter-flexible": "^7.1.1", "redis": "^4.6.10", "redis-memory-server": "^0.12.1", "sequelize": "^6.35.2", "sharp": "^0.33.0", "socket.io": "^4.7.4", "speakeasy": "^2.0.0", "sqlite3": "^5.1.6", "tar": "^6.2.0", "twitter-api-v2": "^1.24.0", "typeorm": "^0.3.17", "unzipper": "^0.10.14", "user-agents": "^1.1.0", "uuid": "^9.0.1", "validator": "^13.11.0", "winston": "^3.11.0", "xlsx": "^0.18.5", "xml2js": "^0.6.2", "zod": "^3.25.75"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@jest/globals": "^30.0.4", "@testcontainers/postgresql": "^11.2.1", "@testcontainers/redis": "^11.2.1", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-validator": "^2.20.33", "@types/ioredis": "^4.28.10", "@types/ioredis-mock": "^8.2.6", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.11", "@types/node": "^20.19.4", "@types/node-cron": "^3.0.11", "@types/node-fetch": "^2.6.12", "@types/pg": "^8.15.4", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "artillery": "^2.0.0", "concurrently": "^8.2.2", "eslint": "^9.17.0", "ioredis-mock": "^8.9.0", "jest": "^29.7.0", "jest-html-reporters": "^3.1.7", "jest-junit": "^16.0.0", "jest-mock-extended": "^4.0.0", "nodemon": "^3.0.2", "prisma": "^6.11.1", "rimraf": "^5.0.5", "supertest": "^7.1.3", "testcontainers": "^11.2.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}