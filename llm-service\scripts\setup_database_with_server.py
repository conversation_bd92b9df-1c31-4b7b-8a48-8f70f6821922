#!/usr/bin/env python3
"""
Setup Database Integration with Provided PostgreSQL Server

This script sets up the database integration using the provided PostgreSQL server:
SG-heady-war-6640-7193-pgsql-master.servers.mongodirector.com
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add the parent directory to the path to import our modules
sys.path.append(str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_database_connection():
    """Test connection to the provided PostgreSQL server"""
    
    logger.info("🚀 Testing Connection to PostgreSQL Server")
    logger.info("=" * 60)
    
    # Database connection details
    db_host = 'SG-heady-war-6640-7193-pgsql-master.servers.mongodirector.com'
    db_port = '5432'
    db_user = 'sgpostgres'
    db_password = 'S_FaNxa8KLukXTuF'

    # Try different database names
    db_names = ['script_ai_platform', 'postgres', 'sgpostgres']
    
    logger.info(f"🔗 Server: {db_host}")
    logger.info(f"📊 Port: {db_port}")
    logger.info(f"👤 User: {db_user}")

    logger.info("\n🔍 Testing Connection with Provided Credentials...")

    try:
        import asyncpg

        # Try connecting to different databases
        for db_name in db_names:
            try:
                logger.info(f"   Trying database: {db_name}")
                connection_string = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

                conn = await asyncpg.connect(connection_string, command_timeout=10)
                result = await conn.fetchval("SELECT version()")

                # If we connected to 'postgres' or 'sgpostgres', try to create our target database
                if db_name in ['postgres', 'sgpostgres'] and 'script_ai_platform' in db_names:
                    try:
                        await conn.execute("CREATE DATABASE script_ai_platform")
                        logger.info("   ✅ Created database 'script_ai_platform'")
                        await conn.close()

                        # Now connect to the new database
                        target_connection = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/script_ai_platform"
                        conn = await asyncpg.connect(target_connection, command_timeout=10)
                        db_name = 'script_ai_platform'
                        connection_string = target_connection
                    except Exception as create_error:
                        logger.info(f"   Database 'script_ai_platform' may already exist: {create_error}")
                        # Continue with current connection

                await conn.close()

                logger.info(f"✅ Connection successful to database: {db_name}")
                logger.info(f"   PostgreSQL Version: {result.split(',')[0]}")

                # Set environment variables
                os.environ['POSTGRES_HOST'] = db_host
                os.environ['POSTGRES_PORT'] = db_port
                os.environ['POSTGRES_DB'] = db_name
                os.environ['POSTGRES_USER'] = db_user
                os.environ['POSTGRES_PASSWORD'] = db_password

                return connection_string

            except Exception as error:
                logger.info(f"   ❌ Failed to connect to {db_name}: {str(error)[:80]}...")
                continue

    except Exception as error:
        logger.error(f"❌ Connection failed: {error}")
        logger.info("\n💡 Possible solutions:")
        logger.info("   1. Check if the server is accessible from your network")
        logger.info("   2. Verify the database 'script_ai_platform' exists")
        logger.info("   3. Check firewall settings")
        return None
        
        logger.error("❌ All connection attempts failed")
        logger.info("\n💡 Possible solutions:")
        logger.info("   1. Check if the server is accessible from your network")
        logger.info("   2. Verify the correct password")
        logger.info("   3. Check if the database 'script_ai_platform' exists")
        logger.info("   4. Ensure PostgreSQL is running on port 5432")
        
        return None
        
    except ImportError:
        logger.error("❌ asyncpg not installed. Installing...")
        import subprocess
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'asyncpg'])
        logger.info("✅ asyncpg installed. Please run the script again.")
        return None

async def setup_database_tables(connection_string: str):
    """Set up the database tables"""
    
    logger.info("\n📊 Setting up Database Tables")
    logger.info("-" * 40)
    
    try:
        from scripts.register_llm_service_to_database import UniversalServiceRegistry
        from services.coc.database.models import PlatformSchemaDB
        
        # Step 1: Universal Service Registry
        logger.info("1. Creating Universal Service Registry...")
        registry = UniversalServiceRegistry(connection_string)
        await registry.initialize()
        await registry.register_llm_service()
        await registry.close()
        logger.info("   ✅ Universal Service Registry complete")
        
        # Step 2: Enhanced Platform Schema Generator Tables
        logger.info("2. Creating Enhanced Platform Schema Generator Tables...")
        platform_db = PlatformSchemaDB(connection_string)
        await platform_db.initialize()
        await platform_db.close()
        logger.info("   ✅ Enhanced Platform Schema Generator Tables complete")
        
        return True
        
    except Exception as error:
        logger.error(f"❌ Database setup failed: {error}")
        return False

async def verify_integration(connection_string: str):
    """Verify the database integration"""
    
    logger.info("\n🔍 Verifying Database Integration")
    logger.info("-" * 40)
    
    try:
        import asyncpg
        conn = await asyncpg.connect(connection_string)
        
        # Check universal service registry
        service_count = await conn.fetchval("""
            SELECT COUNT(*) FROM platform_services 
            WHERE service_name = 'llm-service'
        """)
        
        endpoint_count = await conn.fetchval("""
            SELECT COUNT(*) FROM platform_service_endpoints 
            WHERE service_name = 'llm-service'
        """)
        
        # Check Enhanced Platform Schema Generator tables
        coc_tables = await conn.fetch("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name LIKE 'coc_%'
            ORDER BY table_name
        """)
        
        # Check platform tables
        platform_tables = await conn.fetch("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name LIKE 'platform_%'
            ORDER BY table_name
        """)
        
        await conn.close()
        
        logger.info(f"✅ LLM Service: {service_count} service, {endpoint_count} endpoints")
        logger.info(f"✅ COC Tables: {len(coc_tables)} tables")
        logger.info(f"✅ Platform Tables: {len(platform_tables)} tables")
        
        if len(coc_tables) > 0:
            logger.info("   COC Tables:")
            for table in coc_tables:
                logger.info(f"     - {table['table_name']}")
        
        if len(platform_tables) > 0:
            logger.info("   Platform Tables:")
            for table in platform_tables:
                logger.info(f"     - {table['table_name']}")
        
        return service_count > 0 and len(coc_tables) > 0
        
    except Exception as error:
        logger.error(f"❌ Verification failed: {error}")
        return False

async def main():
    """Main setup function"""
    
    logger.info("🎯 Enhanced Platform Schema Generator Database Integration")
    logger.info("Server: SG-heady-war-6640-7193-pgsql-master.servers.mongodirector.com")
    logger.info("=" * 80)
    
    try:
        # Step 1: Test database connection
        connection_string = await test_database_connection()
        if not connection_string:
            logger.error("❌ Could not establish database connection")
            return False
        
        # Step 2: Set up database tables
        setup_success = await setup_database_tables(connection_string)
        if not setup_success:
            logger.error("❌ Database setup failed")
            return False
        
        # Step 3: Verify integration
        verify_success = await verify_integration(connection_string)
        if not verify_success:
            logger.error("❌ Database integration verification failed")
            return False
        
        # Success!
        logger.info("\n" + "=" * 80)
        logger.info("🎉 Database Integration Complete!")
        logger.info("=" * 80)
        
        logger.info("\n✅ What was accomplished:")
        logger.info("   - Connected to PostgreSQL server successfully")
        logger.info("   - Created universal service registry tables")
        logger.info("   - Registered LLM service with full configuration")
        logger.info("   - Created Enhanced Platform Schema Generator tables")
        logger.info("   - Verified all database components")
        
        logger.info("\n🚀 Next Steps:")
        logger.info("   1. The Enhanced Platform Schema Generator is now database-enabled")
        logger.info("   2. Restart the LLM service to activate PostgreSQL integration")
        logger.info("   3. All service discovery data will be persisted")
        logger.info("   4. Cache and statistics will use PostgreSQL")
        
        logger.info(f"\n📊 Database Connection Details:")
        logger.info(f"   Host: {os.getenv('POSTGRES_HOST')}")
        logger.info(f"   Port: {os.getenv('POSTGRES_PORT')}")
        logger.info(f"   Database: {os.getenv('POSTGRES_DB')}")
        logger.info(f"   User: {os.getenv('POSTGRES_USER')}")
        
        return True
        
    except KeyboardInterrupt:
        logger.info("\n❌ Setup cancelled by user")
        return False
    except Exception as error:
        logger.error(f"\n❌ Unexpected error: {error}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n🎯 Database integration setup completed successfully!")
        print("You can now restart the LLM service to use PostgreSQL.")
    else:
        print("\n❌ Database integration setup failed!")
        sys.exit(1)
