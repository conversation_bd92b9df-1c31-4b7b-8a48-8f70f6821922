/**
 * PostgreSQL Connection Test (psql equivalent)
 * 
 * Mimics: psql -W -U sgpostgres -p 6432 -h SG-rough-guava-317-7201-pgsql-master.servers.mongodirector.com -d postgres
 */

require('dotenv').config();
const { Client } = require('pg');

async function testPsqlConnection() {
  console.log('🚀 PostgreSQL Connection Test (psql equivalent)');
  console.log('================================================');
  console.log('Command: psql -W -U sgpostgres -p 6432 -h SG-rough-guava-317-7201-pgsql-master.servers.mongodirector.com -d postgres');
  console.log('================================================\n');

  const connectionConfig = {
    host: 'SG-rough-guava-317-7201-pgsql-master.servers.mongodirector.com',
    port: 6432,
    database: 'postgres',
    user: 'sgpostgres',
    password: process.env.SCALEGRID_PG_PASSWORD || 'd3tnRHMiuiNI+OTc',
    ssl: false, // SSL disabled via sslmode=disable in connection string
    connectionTimeoutMillis: 30000,
    query_timeout: 15000
  };

  console.log('📋 Connection Parameters:');
  console.log(`   Host: ${connectionConfig.host}`);
  console.log(`   Port: ${connectionConfig.port}`);
  console.log(`   Database: ${connectionConfig.database}`);
  console.log(`   User: ${connectionConfig.user}`);
  console.log(`   Password: ${connectionConfig.password ? '[PROVIDED]' : '[MISSING]'}`);
  console.log(`   SSL: ${connectionConfig.ssl}`);
  console.log('');

  const client = new Client(connectionConfig);
  const startTime = Date.now();

  try {
    
    console.log('🔄 Connecting to PostgreSQL...');
    await Promise.race([
      client.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout after 30s')), 30000)
      )
    ]);

    const connectTime = Date.now() - startTime;
    console.log(`✅ Connected successfully (${connectTime}ms)`);

    // Test basic queries
    console.log('\n🔄 Testing basic queries...');
    
    // Query 1: Version
    const versionResult = await client.query('SELECT version() as version');
    console.log(`📊 PostgreSQL Version: ${versionResult.rows[0].version}`);

    // Query 2: Current database
    const dbResult = await client.query('SELECT current_database() as database');
    console.log(`📊 Current Database: ${dbResult.rows[0].database}`);

    // Query 3: Current user
    const userResult = await client.query('SELECT current_user as user');
    console.log(`📊 Current User: ${userResult.rows[0].user}`);

    // Query 4: List tables
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    console.log(`📊 Tables in database: ${tablesResult.rows.length} tables`);
    if (tablesResult.rows.length > 0) {
      tablesResult.rows.forEach(row => {
        console.log(`   - ${row.table_name}`);
      });
    } else {
      console.log('   (No tables found - database is empty)');
    }

    // Query 5: Test write permissions
    console.log('\n🔄 Testing write permissions...');
    try {
      await client.query('CREATE TABLE IF NOT EXISTS connection_test (id SERIAL PRIMARY KEY, test_time TIMESTAMP DEFAULT NOW())');
      await client.query('INSERT INTO connection_test DEFAULT VALUES');
      const testResult = await client.query('SELECT COUNT(*) as count FROM connection_test');
      console.log(`✅ Write test successful - ${testResult.rows[0].count} records in test table`);
      await client.query('DROP TABLE connection_test');
      console.log('✅ Cleanup successful - test table removed');
    } catch (writeError) {
      console.log(`⚠️ Write test failed: ${writeError.message}`);
    }

    const totalTime = Date.now() - startTime;
    console.log(`\n🎉 Connection test completed successfully (${totalTime}ms total)`);
    console.log('✅ ScaleGrid PostgreSQL is ready for use!');

    return { success: true, latency: totalTime };

  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.log(`\n❌ Connection test failed (${totalTime}ms)`);
    console.log(`   Error: ${error.message}`);
    console.log(`   Error Type: ${error.constructor.name}`);
    if (error.code) console.log(`   Error Code: ${error.code}`);
    
    // Provide troubleshooting hints
    console.log('\n🔍 Troubleshooting:');
    if (error.message.includes('ETIMEDOUT')) {
      console.log('   - Connection timeout: Check firewall/network settings');
      console.log('   - Verify IP whitelist includes: *************/32');
    } else if (error.message.includes('authentication')) {
      console.log('   - Authentication failed: Check username/password');
    } else if (error.message.includes('database')) {
      console.log('   - Database access issue: Check database name and permissions');
    }

    return { success: false, error: error.message, latency: totalTime };

  } finally {
    try {
      await client.end();
      console.log('🔌 Connection closed');
    } catch (closeError) {
      console.log(`⚠️ Error closing connection: ${closeError.message}`);
    }
  }
}

// Run the test
testPsqlConnection()
  .then(result => {
    if (result.success) {
      console.log('\n🎯 RESULT: PostgreSQL connection successful!');
      process.exit(0);
    } else {
      console.log('\n💥 RESULT: PostgreSQL connection failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('💥 Test script crashed:', error);
    process.exit(1);
  });
