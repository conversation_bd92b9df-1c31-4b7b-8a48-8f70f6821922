"""
AI Model Ensemble Service

Intelligent Gemini model selection and routing system that:
- Optimizes model selection based on task complexity and intent
- Manages rate limits across all Gemini 2.5 models (Pro, Flash, Flash-Lite)
- Provides fallback mechanisms for high availability
- Tracks model performance and costs in real-time
- Implements free tier optimization strategies
"""

import asyncio
import logging
import time
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class ModelConfig:
    name: str
    display_name: str
    max_tokens: int
    context_window: int
    free_tier: Dict[str, int]  # rpm, rpd, tpm
    paid_tier: Dict[str, int]  # rpm, rpd, tpm
    cost_per_1k_tokens: float
    specialization: str
    priority: int
    use_case: List[str]
    enabled: bool

@dataclass
class ModelUsage:
    model: str
    request_count: int
    token_count: int
    last_used: float
    success_rate: float
    average_response_time: float
    daily_usage: int
    minutely_usage: int

@dataclass
class ModelSelection:
    selected_model: str
    confidence: float
    reasoning: str
    alternatives: List[Dict[str, Any]]
    estimated_cost: float
    estimated_time: int

class TokenBucket:
    """Token bucket implementation for rate limiting"""
    
    def __init__(self, capacity: int, refill_rate: float, max_burst: int):
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate
        self.max_burst = max_burst
        self.last_refill = time.time()

    def consume(self, tokens: int) -> bool:
        """Try to consume tokens, return True if successful"""
        self._refill()
        
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        
        return False

    def get_available_tokens(self) -> int:
        """Get available tokens"""
        self._refill()
        return int(self.tokens)

    def get_wait_time(self, tokens: int) -> float:
        """Get wait time in seconds for tokens to be available"""
        self._refill()
        
        if self.tokens >= tokens:
            return 0
        
        needed = tokens - self.tokens
        return needed / self.refill_rate

    def _refill(self) -> None:
        """Refill tokens based on time elapsed"""
        now = time.time()
        elapsed = now - self.last_refill
        tokens_to_add = elapsed * self.refill_rate
        
        self.tokens = min(self.capacity, self.tokens + tokens_to_add)
        self.last_refill = now

class AIModelEnsemble:
    """Intelligent Gemini model selection and routing system"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.AIModelEnsemble")
        self.model_configs: Dict[str, ModelConfig] = {}
        self.model_usage: Dict[str, ModelUsage] = {}
        self.rate_limiters: Dict[str, TokenBucket] = {}
        self.performance_history: Dict[str, deque] = {}
        self.is_initialized = False

    async def initialize(self) -> None:
        """Initialize AI Model Ensemble"""
        self.logger.info("Initializing AI Model Ensemble...")
        
        try:
            # Load Gemini 2.5 model configurations
            await self._load_gemini_model_configurations()
            
            # Initialize rate limiters for free tier optimization
            self._initialize_rate_limiters()
            
            # Initialize usage tracking
            self._initialize_usage_tracking()
            
            # Start periodic cleanup and optimization
            asyncio.create_task(self._periodic_optimization())
            
            self.is_initialized = True
            
            enabled_models = [name for name, config in self.model_configs.items() if config.enabled]
            self.logger.info("AI Model Ensemble initialized successfully", extra={
                'model_count': len(self.model_configs),
                'enabled_models': enabled_models
            })
            
        except Exception as error:
            self.logger.error(f"AI Model Ensemble initialization failed: {error}")
            raise

    async def select_optimal_model(
        self,
        request,
        intent_classification,
        platform_capabilities: List[Any]
    ) -> str:
        """Select optimal Gemini model for a request based on intelligence analysis"""
        
        if not self.is_initialized:
            raise Exception("AI Model Ensemble not initialized")

        try:
            self.logger.debug("Selecting optimal Gemini model", extra={
                'request_id': request.id,
                'intent': intent_classification.intent,
                'complexity': intent_classification.complexity
            })

            # Analyze model selection requirements
            selection = await self._analyze_model_selection(
                request,
                intent_classification,
                platform_capabilities
            )

            # Check rate limits and availability
            available_model = await self._check_availability_and_rate_limits(
                selection.selected_model,
                [alt['model'] for alt in selection.alternatives],
                request
            )

            # Update usage tracking
            self._update_usage_tracking(available_model, request)

            self.logger.debug("Gemini model selected", extra={
                'request_id': request.id,
                'selected_model': available_model,
                'original_choice': selection.selected_model,
                'reasoning': selection.reasoning
            })

            return available_model

        except Exception as error:
            self.logger.error(f"Model selection failed: {error}", extra={
                'request_id': request.id
            })

            # Fallback to most available model
            return self._get_fallback_model()

    async def _load_gemini_model_configurations(self) -> None:
        """Load Gemini 2.5 model configurations based on our successful testing"""
        configs = [
            ModelConfig(
                name='gemini-2.5-pro',
                display_name='Gemini 2.5 Pro',
                max_tokens=65536,
                context_window=1048576,
                free_tier={'rpm': 10, 'rpd': 100, 'tpm': 2000000},
                paid_tier={'rpm': 1000, 'rpd': 50000, 'tpm': 10000000},
                cost_per_1k_tokens=0.0,  # Free tier
                specialization='strategic_planning',
                priority=1,
                use_case=['create_campaign', 'get_insights', 'analyze_performance'],
                enabled=os.getenv('COC_GEMINI_PRO_2_5_ENABLED', 'true').lower() == 'true'
            ),
            ModelConfig(
                name='gemini-2.5-flash',
                display_name='Gemini 2.5 Flash',
                max_tokens=8192,
                context_window=1048576,
                free_tier={'rpm': 15, 'rpd': 1500, 'tpm': 1000000},
                paid_tier={'rpm': 2000, 'rpd': 100000, 'tpm': 20000000},
                cost_per_1k_tokens=0.0,  # Free tier
                specialization='qa_guidance',
                priority=2,
                use_case=['generate_content', 'general_query', 'automation_control'],
                enabled=os.getenv('COC_GEMINI_FLASH_2_5_ENABLED', 'true').lower() == 'true'
            ),
            ModelConfig(
                name='gemini-2.5-flash-lite',
                display_name='Gemini 2.5 Flash Lite',
                max_tokens=4096,
                context_window=262144,
                free_tier={'rpm': 30, 'rpd': 3000, 'tpm': 500000},
                paid_tier={'rpm': 5000, 'rpd': 200000, 'tpm': ********},
                cost_per_1k_tokens=0.0,  # Free tier
                specialization='quick_responses',
                priority=3,
                use_case=['system_status', 'manage_accounts', 'general_query'],
                enabled=os.getenv('COC_GEMINI_FLASH_LITE_2_5_ENABLED', 'true').lower() == 'true'
            )
        ]

        # Load configurations into map
        for config in configs:
            if config.enabled:
                self.model_configs[config.name] = config
                
                # Initialize usage tracking
                self.model_usage[config.name] = ModelUsage(
                    model=config.name,
                    request_count=0,
                    token_count=0,
                    last_used=time.time(),
                    success_rate=1.0,
                    average_response_time=0,
                    daily_usage=0,
                    minutely_usage=0
                )

        self.logger.debug("Gemini model configurations loaded", extra={
            'total_configs': len(configs),
            'enabled_configs': len(self.model_configs)
        })

    def _initialize_rate_limiters(self) -> None:
        """Initialize rate limiters for free tier optimization"""
        for model_name, config in self.model_configs.items():
            self.rate_limiters[model_name] = TokenBucket(
                capacity=config.free_tier['tpm'],
                refill_rate=config.free_tier['tpm'] / 60,  # Per second
                max_burst=config.free_tier['rpm']
            )

        self.logger.debug("Rate limiters initialized for free tier optimization", extra={
            'count': len(self.rate_limiters)
        })

    async def _analyze_model_selection(
        self,
        request,
        intent_classification,
        platform_capabilities: List[Any]
    ) -> ModelSelection:
        """Analyze model selection based on request characteristics and COC intelligence"""
        
        scores = []

        for model_name, config in self.model_configs.items():
            score = 0
            reasons = []

            # Base priority score (higher priority = higher score)
            score += (4 - config.priority) * 0.25
            reasons.append(f"Priority: {config.priority}")

            # Intent-based use case matching
            if intent_classification.intent in config.use_case:
                score += 0.4
                reasons.append(f"Specialized for {intent_classification.intent}")

            # Complexity-based scoring
            complexity_score = self._get_complexity_score(intent_classification.complexity, config)
            score += complexity_score
            reasons.append(f"Complexity match: {complexity_score:.2f}")

            # Performance history scoring
            usage = self.model_usage.get(model_name)
            if usage:
                score += usage.success_rate * 0.2
                # Penalize slow models
                response_time_penalty = min(0.1, (usage.average_response_time / 10000) * 0.1)
                score -= response_time_penalty
                reasons.append(f"Success rate: {usage.success_rate * 100:.1f}%")
                reasons.append(f"Avg response: {usage.average_response_time:.0f}ms")

            # Rate limit availability scoring
            rate_limiter = self.rate_limiters.get(model_name)
            if rate_limiter:
                available_tokens = rate_limiter.get_available_tokens()
                estimated_tokens = self._estimate_token_usage(request.input)
                
                if available_tokens > estimated_tokens * 2:
                    score += 0.15
                    reasons.append('Rate limit comfortable')
                elif available_tokens > estimated_tokens:
                    score += 0.05
                    reasons.append('Rate limit available')
                else:
                    score -= 0.3
                    reasons.append('Rate limit constrained')

            # Request priority boost
            if request.priority in ['high', 'critical']:
                if config.priority == 1:  # Gemini Pro
                    score += 0.2
                    reasons.append('High priority request')

            # Source-based optimization
            if request.source == 'telegram' and 'flash' in config.name:
                score += 0.1
                reasons.append('Optimized for Telegram')

            scores.append({'model': model_name, 'score': score, 'reasons': reasons})

        # Sort by score
        scores.sort(key=lambda x: x['score'], reverse=True)

        best_model = scores[0]
        alternatives = scores[1:3]

        return ModelSelection(
            selected_model=best_model['model'],
            confidence=min(1.0, best_model['score']),
            reasoning=', '.join(best_model['reasons']),
            alternatives=[
                {'model': alt['model'], 'score': alt['score'], 'reason': ', '.join(alt['reasons'])}
                for alt in alternatives
            ],
            estimated_cost=0,  # Free tier
            estimated_time=self._estimate_response_time(best_model['model'], intent_classification.complexity)
        )

    def _get_complexity_score(self, complexity: str, config: ModelConfig) -> float:
        """Get complexity score for model matching"""
        complexity_scores = {
            'simple': 0.4 if 'lite' in config.name else 0.2,
            'moderate': 0.4 if 'flash' in config.name and 'lite' not in config.name else 0.3,
            'complex': 0.4 if 'pro' in config.name else 0.1,
            'enterprise': 0.4 if 'pro' in config.name else 0.1
        }
        return complexity_scores.get(complexity, 0.2)

    async def _check_availability_and_rate_limits(
        self,
        preferred_model: str,
        alternatives: List[str],
        request
    ) -> str:
        """Check availability and rate limits with intelligent fallback"""
        
        estimated_tokens = self._estimate_token_usage(request.input)

        # Try preferred model first
        preferred_limiter = self.rate_limiters.get(preferred_model)
        if preferred_limiter and preferred_limiter.consume(estimated_tokens):
            return preferred_model

        # Try alternatives in order of preference
        for alternative in alternatives:
            limiter = self.rate_limiters.get(alternative)
            if limiter and limiter.consume(estimated_tokens):
                self.logger.warning("Using alternative model due to rate limits", extra={
                    'preferred': preferred_model,
                    'selected': alternative,
                    'request_id': request.id
                })
                return alternative

        # If all models are rate limited, use the one with shortest wait time
        wait_times = []
        for model, limiter in self.rate_limiters.items():
            wait_time = limiter.get_wait_time(estimated_tokens)
            wait_times.append({'model': model, 'wait_time': wait_time})

        wait_times.sort(key=lambda x: x['wait_time'])
        
        self.logger.warning("All models rate limited, using shortest wait time", extra={
            'selected_model': wait_times[0]['model'],
            'wait_time': wait_times[0]['wait_time'],
            'request_id': request.id
        })

        return wait_times[0]['model']

    def _estimate_token_usage(self, input_text: str) -> int:
        """Estimate token usage for a request"""
        # Rough estimation: 1 token ≈ 4 characters for English text
        input_tokens = len(input_text) // 4
        output_tokens = min(500, input_tokens // 2)  # Estimate output
        return input_tokens + output_tokens + 50  # Add buffer

    def _estimate_response_time(self, model: str, complexity: str) -> int:
        """Estimate response time based on model and complexity"""
        base_time = 1000 if 'lite' in model else 2000 if 'flash' in model else 3000
        
        complexity_multiplier = {
            'simple': 1,
            'moderate': 1.5,
            'complex': 2,
            'enterprise': 2.5
        }.get(complexity, 1)

        return int(base_time * complexity_multiplier)

    def _update_usage_tracking(self, model: str, request) -> None:
        """Update usage tracking for performance monitoring"""
        usage = self.model_usage.get(model)
        if usage:
            usage.request_count += 1
            usage.last_used = time.time()
            usage.minutely_usage += 1
            usage.daily_usage += 1
            
            estimated_tokens = self._estimate_token_usage(request.input)
            usage.token_count += estimated_tokens

    def _initialize_usage_tracking(self) -> None:
        """Initialize usage tracking with periodic resets"""
        async def reset_usage():
            while self.is_initialized:
                try:
                    # Reset minutely usage every minute
                    await asyncio.sleep(60)
                    for usage in self.model_usage.values():
                        usage.minutely_usage = 0
                        
                    # Reset daily usage every day (simplified - runs every hour for demo)
                    if int(time.time()) % 3600 == 0:  # Every hour
                        for usage in self.model_usage.values():
                            usage.daily_usage = 0
                            
                except Exception as error:
                    self.logger.error(f"Usage tracking reset failed: {error}")
        
        asyncio.create_task(reset_usage())

    async def _periodic_optimization(self) -> None:
        """Start periodic optimization and cleanup"""
        while self.is_initialized:
            try:
                await asyncio.sleep(3600)  # Clean up every hour
                cutoff = time.time() - (24 * 60 * 60)  # 24 hours
                
                for model, metrics in self.performance_history.items():
                    # Keep only recent metrics
                    while metrics and metrics[0]['timestamp'] < cutoff:
                        metrics.popleft()
                        
            except Exception as error:
                self.logger.error(f"Periodic optimization failed: {error}")

    def _get_fallback_model(self) -> str:
        """Get fallback model when all else fails"""
        # Return the model with highest rate limits (most likely to be available)
        sorted_models = sorted(
            self.model_configs.items(),
            key=lambda x: x[1].free_tier['rpm'],
            reverse=True
        )
        
        return sorted_models[0][0] if sorted_models else 'gemini-2.5-flash-lite'

    async def record_performance(self, model: str, response_time: int, success: bool) -> None:
        """Record performance metrics for continuous improvement"""
        if model not in self.performance_history:
            self.performance_history[model] = deque(maxlen=1000)

        metrics = self.performance_history[model]
        metrics.append({
            'timestamp': time.time(),
            'response_time': response_time,
            'success': success
        })

        # Update usage statistics
        usage = self.model_usage.get(model)
        if usage and metrics:
            successful_metrics = [m for m in metrics if m['success']]
            
            usage.success_rate = len(successful_metrics) / len(metrics)
            usage.average_response_time = sum(m['response_time'] for m in metrics) / len(metrics)

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for monitoring"""
        model_metrics = {}
        
        for model, usage in self.model_usage.items():
            model_metrics[model] = {
                'request_count': usage.request_count,
                'success_rate': usage.success_rate,
                'average_response_time': usage.average_response_time,
                'daily_usage': usage.daily_usage,
                'token_count': usage.token_count,
                'last_used': usage.last_used
            }
        
        total_requests = sum(u.request_count for u in self.model_usage.values())
        avg_success_rate = (
            sum(u.success_rate for u in self.model_usage.values()) / len(self.model_usage)
            if self.model_usage else 0
        )
        
        return {
            'models': model_metrics,
            'total_requests': total_requests,
            'average_success_rate': avg_success_rate
        }

    def get_status(self) -> Dict[str, Any]:
        """Get ensemble status"""
        rate_limits = {}
        for model, limiter in self.rate_limiters.items():
            rate_limits[model] = {
                'available_tokens': limiter.get_available_tokens(),
                'capacity': limiter.capacity
            }
        
        return {
            'initialized': self.is_initialized,
            'model_count': len(self.model_configs),
            'enabled_models': list(self.model_configs.keys()),
            'usage': {model: {
                'request_count': usage.request_count,
                'success_rate': usage.success_rate,
                'daily_usage': usage.daily_usage
            } for model, usage in self.model_usage.items()},
            'rate_limits': rate_limits
        }

    async def shutdown(self) -> None:
        """Shutdown AI Model Ensemble"""
        self.logger.info("AI Model Ensemble shutting down...")
        self.model_configs.clear()
        self.model_usage.clear()
        self.rate_limiters.clear()
        self.performance_history.clear()
        self.is_initialized = False
