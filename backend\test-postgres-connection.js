/**
 * Simple PostgreSQL Connection Test
 * Direct test of ScaleGrid PostgreSQL server
 */

const { Pool } = require('pg');

async function testPostgreSQLConnection() {
  console.log('🐘 Testing PostgreSQL Connection');
  console.log('================================');
  
  const config = {
    host: '************', // Direct IP from DNS lookup
    port: 6432,
    database: 'postgres',
    user: 'sgpostgres',
    password: 'd3tnRHMiuiNI+OTc',
    ssl: false,
    connectionTimeoutMillis: 15000,
    max: 1
  };

  console.log(`Host: ${config.host}`);
  console.log(`Port: ${config.port}`);
  console.log(`Database: ${config.database}`);
  console.log(`User: ${config.user}`);
  console.log('');

  let pool;
  let client;

  try {
    console.log('🔄 Creating connection pool...');
    pool = new Pool(config);

    console.log('🔄 Connecting to PostgreSQL...');
    const startTime = Date.now();
    
    client = await pool.connect();
    const connectTime = Date.now() - startTime;
    
    console.log(`✅ Connected successfully in ${connectTime}ms`);

    // Test basic query
    console.log('🔄 Testing basic query...');
    const result = await client.query('SELECT version() as version, current_database() as database, current_user as user');
    
    console.log('📊 Database Info:');
    console.log(`   Version: ${result.rows[0].version.split(' ')[0]} ${result.rows[0].version.split(' ')[1]}`);
    console.log(`   Database: ${result.rows[0].database}`);
    console.log(`   User: ${result.rows[0].user}`);

    // Test table creation (to verify write permissions)
    console.log('🔄 Testing write permissions...');
    await client.query('CREATE TABLE IF NOT EXISTS connection_test (id SERIAL PRIMARY KEY, test_time TIMESTAMP DEFAULT NOW())');
    await client.query('INSERT INTO connection_test DEFAULT VALUES');
    const testResult = await client.query('SELECT COUNT(*) as count FROM connection_test');
    console.log(`✅ Write test successful - ${testResult.rows[0].count} records in test table`);

    // Cleanup
    await client.query('DROP TABLE IF EXISTS connection_test');
    console.log('✅ Cleanup completed');

    const totalTime = Date.now() - startTime;
    console.log(`\n🎉 PostgreSQL connection test PASSED (${totalTime}ms total)`);
    console.log('✅ Server is working correctly!');
    
    return true;

  } catch (error) {
    console.log(`\n❌ PostgreSQL connection FAILED`);
    console.log(`   Error: ${error.message}`);
    console.log(`   Code: ${error.code || 'N/A'}`);
    console.log(`   Type: ${error.constructor.name}`);
    
    if (error.code === 'ENOTFOUND') {
      console.log('💡 DNS resolution failed - check hostname');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('💡 Connection refused - check if server is running and port is correct');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('💡 Connection timeout - check network connectivity');
    } else if (error.code === '28P01') {
      console.log('💡 Authentication failed - check username/password');
    }
    
    return false;

  } finally {
    if (client) {
      try {
        client.release();
        console.log('🔄 Client connection released');
      } catch (e) {
        console.log('⚠️ Error releasing client:', e.message);
      }
    }
    
    if (pool) {
      try {
        await pool.end();
        console.log('🔄 Connection pool closed');
      } catch (e) {
        console.log('⚠️ Error closing pool:', e.message);
      }
    }
  }
}

// Run the test
testPostgreSQLConnection()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
