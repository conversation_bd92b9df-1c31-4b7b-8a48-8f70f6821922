"""
Cognitive Orchestration Core (COC) Package

Phase 1 - Core Infrastructure:
- Cognitive Orchestration Core: Main AI consciousness system
- COC Router: Intelligent request routing and orchestration
- Intent Analysis Engine: Advanced natural language understanding
- AI Model Ensemble: Intelligent Gemini model selection
- Platform Schema Generator: Dynamic platform introspection

This package transforms the LLM service into an autonomous AI consciousness
system with total platform awareness and intelligent decision making.
"""

from .cognitive_orchestration_core import (
    CognitiveOrchestrationCore,
    COCConfig,
    COCRequest,
    COCResponse,
    IntentClassification,
    Entity,
    PlatformCapability,
    COCAction,
    get_coc_instance,
    initialize_coc,
    initialize_coc_async
)

from .coc_router import COCRouter, ExecutionStep, ExecutionPlan
from .intent_analysis_engine import IntentAnalysisEngine, IntentPattern
from .ai_model_ensemble import AIModelEnsemble, ModelConfig, ModelUsage, ModelSelection, TokenBucket
from .platform_schema_generator import (
    PlatformSchemaGenerator,
    ServiceSchema,
    EndpointSchema,
    ServiceAvailability,
    CapabilityMapping
)

__version__ = "1.0.0"
__phase__ = "Phase 1 - Core Infrastructure"

__all__ = [
    # Core COC classes
    'CognitiveOrchestrationCore',
    'COCConfig',
    'COCRequest',
    'COCResponse',
    'IntentClassification',
    'Entity',
    'PlatformCapability',
    'COCAction',
    
    # COC Router
    'COCRouter',
    'ExecutionStep',
    'ExecutionPlan',
    
    # Intent Analysis Engine
    'IntentAnalysisEngine',
    'IntentPattern',
    
    # AI Model Ensemble
    'AIModelEnsemble',
    'ModelConfig',
    'ModelUsage',
    'ModelSelection',
    'TokenBucket',
    
    # Platform Schema Generator
    'PlatformSchemaGenerator',
    'ServiceSchema',
    'EndpointSchema',
    'ServiceAvailability',
    'CapabilityMapping',
    
    # Utility functions
    'get_coc_instance',
    'initialize_coc',
    'initialize_coc_async'
]
