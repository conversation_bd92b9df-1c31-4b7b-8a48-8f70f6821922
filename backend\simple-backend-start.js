const express = require('express');
const cors = require('cors');
const { PrismaClient } = require('@prisma/client');
const Redis = require('ioredis');
require('dotenv').config();

// Set SSL environment variable for PostgreSQL
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize clients
let prisma;
let redis;

async function initializeServices() {
    console.log('🚀 Starting Simple Backend with ScaleGrid...');
    
    try {
        // Initialize Prisma (PostgreSQL)
        console.log('📡 Connecting to ScaleGrid PostgreSQL...');
        prisma = new PrismaClient({
            log: ['error', 'warn'],
        });
        await prisma.$connect();
        console.log('✅ PostgreSQL connected successfully');
        
        // Test database
        const result = await prisma.$queryRaw`SELECT current_database(), current_user`;
        console.log('📊 Database:', result[0].current_database, 'User:', result[0].current_user);
        
    } catch (error) {
        console.error('❌ PostgreSQL connection failed:', error.message);
    }
    
    try {
        // Initialize Redis
        console.log('📡 Connecting to ScaleGrid Redis...');
        redis = new Redis(process.env.REDIS_URL, {
            connectTimeout: 10000,
            lazyConnect: true
        });
        await redis.connect();
        console.log('✅ Redis connected successfully');
        
        // Test Redis
        const pong = await redis.ping();
        console.log('🏓 Redis PING:', pong);
        
    } catch (error) {
        console.error('❌ Redis connection failed:', error.message);
    }
}

// Routes
app.get('/', (req, res) => {
    res.json({
        message: 'X Marketing Platform Backend - Simple Mode',
        status: 'running',
        timestamp: new Date().toISOString(),
        databases: {
            postgresql: prisma ? 'connected' : 'disconnected',
            redis: redis && redis.status === 'ready' ? 'connected' : 'disconnected'
        }
    });
});

app.get('/health', async (req, res) => {
    const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {}
    };
    
    // Check PostgreSQL
    try {
        if (prisma) {
            await prisma.$queryRaw`SELECT 1`;
            health.services.postgresql = { status: 'healthy', message: 'Connected to ScaleGrid' };
        } else {
            health.services.postgresql = { status: 'unhealthy', message: 'Not initialized' };
        }
    } catch (error) {
        health.services.postgresql = { status: 'unhealthy', message: error.message };
    }
    
    // Check Redis
    try {
        if (redis && redis.status === 'ready') {
            await redis.ping();
            health.services.redis = { status: 'healthy', message: 'Connected to ScaleGrid' };
        } else {
            health.services.redis = { status: 'unhealthy', message: 'Not connected' };
        }
    } catch (error) {
        health.services.redis = { status: 'unhealthy', message: error.message };
    }
    
    // Overall status
    const allHealthy = Object.values(health.services).every(service => service.status === 'healthy');
    health.status = allHealthy ? 'healthy' : 'degraded';
    
    res.status(allHealthy ? 200 : 503).json(health);
});

app.get('/api/users', async (req, res) => {
    try {
        if (!prisma) {
            return res.status(503).json({ error: 'Database not available' });
        }
        
        const users = await prisma.user.findMany({
            take: 10,
            select: {
                id: true,
                username: true,
                email: true,
                createdAt: true
            }
        });
        
        res.json({
            users,
            count: users.length,
            message: 'Users retrieved from ScaleGrid PostgreSQL'
        });
    } catch (error) {
        res.status(500).json({ 
            error: 'Failed to fetch users', 
            message: error.message 
        });
    }
});

app.get('/api/redis/test', async (req, res) => {
    try {
        if (!redis || redis.status !== 'ready') {
            return res.status(503).json({ error: 'Redis not available' });
        }
        
        const testKey = 'backend_test_' + Date.now();
        const testValue = { message: 'Backend Redis test', timestamp: new Date().toISOString() };
        
        await redis.set(testKey, JSON.stringify(testValue), 'EX', 60);
        const retrieved = JSON.parse(await redis.get(testKey));
        await redis.del(testKey);
        
        res.json({
            message: 'Redis test successful',
            data: retrieved,
            source: 'ScaleGrid Redis'
        });
    } catch (error) {
        res.status(500).json({ 
            error: 'Redis test failed', 
            message: error.message 
        });
    }
});

// Graceful shutdown
process.on('SIGTERM', async () => {
    console.log('🔄 Shutting down gracefully...');
    
    if (prisma) {
        await prisma.$disconnect();
        console.log('✅ PostgreSQL disconnected');
    }
    
    if (redis) {
        await redis.disconnect();
        console.log('✅ Redis disconnected');
    }
    
    process.exit(0);
});

// Start server
async function start() {
    await initializeServices();
    
    app.listen(PORT, () => {
        console.log(`🚀 Simple Backend running on port ${PORT}`);
        console.log(`📊 Health check: http://localhost:${PORT}/health`);
        console.log(`🌐 API endpoint: http://localhost:${PORT}/api/users`);
        console.log(`🔴 Redis test: http://localhost:${PORT}/api/redis/test`);
        console.log('✅ Backend ready for requests!');
    });
}

start().catch(error => {
    console.error('❌ Failed to start backend:', error);
    process.exit(1);
});
